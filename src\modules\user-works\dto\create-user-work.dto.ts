import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsBoolean, IsDateString, IsString, IsArray, <PERSON>, <PERSON> } from "class-validator";
import { ReadingStatus } from "../../../entities";

export class CreateUserWorkDto {
	@ApiProperty({
		description: "ID da obra",
		example: 1,
	})
	@IsNotEmpty()
	@IsNumber()
	workId: number;

	@ApiPropertyOptional({
		description: "Status de leitura",
		enum: ReadingStatus,
		example: ReadingStatus.READING,
	})
	@IsOptional()
	@IsEnum(ReadingStatus)
	status?: ReadingStatus;

	@ApiPropertyOptional({
		description: "Capítulo atual",
		example: 1050,
		minimum: 0,
	})
	@IsOptional()
	@IsNumber()
	@Min(0)
	currentChapter?: number;

	@ApiPropertyOptional({
		description: "Nota dada pelo usuário (1-10)",
		example: 9.5,
		minimum: 1,
		maximum: 10,
	})
	@IsOptional()
	@IsNumber()
	@Min(1)
	@Max(10)
	rating?: number;

	@ApiPropertyOptional({
		description: "Se a obra está marcada como favorita",
		example: true,
	})
	@IsOptional()
	@IsBoolean()
	isFavorite?: boolean;

	@ApiPropertyOptional({
		description: "Data de início da leitura",
		example: "2024-01-15",
	})
	@IsOptional()
	@IsDateString()
	startDate?: string;

	@ApiPropertyOptional({
		description: "Data de conclusão da leitura",
		example: "2024-03-20",
	})
	@IsOptional()
	@IsDateString()
	finishDate?: string;

	@ApiPropertyOptional({
		description: "Comentário pessoal sobre a obra",
		example: "Uma das melhores obras que já li!",
	})
	@IsOptional()
	@IsString()
	personalComment?: string;

	@ApiPropertyOptional({
		description: "Tags personalizadas",
		example: ["favorito", "ação", "épico"],
		type: [String],
	})
	@IsOptional()
	@IsArray()
	@IsString({ each: true })
	tags?: string[];
}

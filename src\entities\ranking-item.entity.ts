import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index } from "typeorm";
import { ApiProperty } from "@nestjs/swagger";
import { Ranking } from "./ranking.entity";
import { Work } from "./work.entity";

@Entity("ranking_items")
@Index(["rankingId", "position"], { unique: true })
@Index(["rankingId", "workId"], { unique: true })
export class RankingItem {
	@ApiProperty({
		description: "ID único do item do ranking",
		example: 1,
	})
	@PrimaryGeneratedColumn()
	id: number;

	@ApiProperty({
		description: "ID do ranking",
		example: 1,
	})
	@Column()
	rankingId: number;

	@ApiProperty({
		description: "ID da obra",
		example: 1,
	})
	@Column()
	workId: number;

	@ApiProperty({
		description: "Posição no ranking",
		example: 1,
	})
	@Column()
	position: number;

	@ApiProperty({
		description: "Comentário sobre a posição",
		example: "Simplesmente perfeito em todos os aspectos!",
	})
	@Column({ type: "text", nullable: true })
	comment?: string;

	@ApiProperty({
		description: "Data de criação",
	})
	@CreateDateColumn()
	createdAt: Date;

	@ApiProperty({
		description: "Data de última atualização",
	})
	@UpdateDateColumn()
	updatedAt: Date;

	// Relacionamentos
	@ManyToOne(() => Ranking, ranking => ranking.items, { onDelete: "CASCADE" })
	@JoinColumn({ name: "rankingId" })
	ranking: Ranking;

	@ManyToOne(() => Work, { onDelete: "CASCADE" })
	@JoinColumn({ name: "workId" })
	work: Work;
}

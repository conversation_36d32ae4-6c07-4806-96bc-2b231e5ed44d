# 📚 Manga Tracker API

API completa para gerenciamento de leitura de mangás, manhwas e manhuas desenvolvida com NestJS, PostgreSQL e documentação Swagger detalhada.

## 🚀 Funcionalidades

### 📖 Gerenciamento de Obras

- Cadastro de mangás, manhwas e manhuas
- Busca avançada com filtros
- Sistema de avaliações e notas médias
- Categorização por gêneros e status

### 👤 Sistema de Usuários

- Registro e autenticação JWT
- Perfis personalizados
- Biblioteca pessoal

### 📚 Biblioteca Pessoal

- Adicionar obras à biblioteca
- Controle de progresso de leitura
- Status de leitura (lendo, completo, pausado, etc.)
- Sistema de favoritos
- Tags personalizadas

### ⭐ Sistema de Avaliações

- Notas de 1 a 10
- Comentários pessoais
- Estatísticas de leitura

### 📝 Anotações (Em desenvolvimento)

- Notas sobre obras e capítulos
- Sistema de spoilers
- Anotações públicas e privadas

### 🏆 Rankings (Em desenvolvimento)

- Listas personalizadas
- Rankings por categorias
- Compartilhamento de listas

## 🛠️ Tecnologias

- **Backend**: NestJS (Node.js + TypeScript)
- **Banco de Dados**: PostgreSQL
- **ORM**: TypeORM
- **Autenticação**: JWT (JSON Web Tokens)
- **Validação**: class-validator
- **Documentação**: Swagger/OpenAPI
- **Containerização**: Docker & Docker Compose

## 📋 Pré-requisitos

- Node.js 18+
- Docker e Docker Compose
- npm ou yarn

## 🚀 Instalação e Execução

### 1. Clone o repositório

```bash
git clone <url-do-repositorio>
cd manga-tracker-api
```

### 2. Instale as dependências

```bash
npm install
```

### 3. Configure as variáveis de ambiente

```bash
cp .env.example .env
# Edite o arquivo .env conforme necessário
```

### 4. Inicie o banco de dados

```bash
docker-compose up -d postgres
```

### 5. Execute a aplicação

```bash
# Desenvolvimento
npm run start:dev

# Produção
npm run build
npm run start:prod
```

## 📚 Documentação da API

Após iniciar a aplicação, acesse:

- **Swagger UI**: http://localhost:3000/api/docs
- **API Base**: http://localhost:3000

### Principais Endpoints

#### Autenticação

- `POST /auth/register` - Registrar usuário
- `POST /auth/login` - Fazer login
- `GET /auth/profile` - Obter perfil

#### Obras

- `GET /works` - Listar obras
- `POST /works` - Criar obra
- `GET /works/:id` - Obter obra
- `GET /works/popular` - Obras populares

#### Biblioteca

- `GET /user-works` - Biblioteca do usuário
- `POST /user-works` - Adicionar à biblioteca
- `PATCH /user-works/:id` - Atualizar obra
- `GET /user-works/statistics` - Estatísticas

## 🗄️ Estrutura do Banco

### Principais Entidades

- **users**: Usuários do sistema
- **works**: Obras (mangás, manhwas, manhuas)
- **user_works**: Relacionamento usuário-obra
- **notes**: Anotações dos usuários
- **rankings**: Rankings personalizados
- **shared_lists**: Listas compartilhadas

## 🧪 Testes

```bash
# Testes unitários
npm run test

# Testes e2e
npm run test:e2e

# Coverage
npm run test:cov
```

## 📦 Scripts Disponíveis

```bash
npm run build          # Build da aplicação
npm run start          # Iniciar aplicação
npm run start:dev      # Modo desenvolvimento
npm run start:debug    # Modo debug
npm run lint           # Linter
npm run format         # Formatação de código
```

## 🐳 Docker

### Desenvolvimento com Docker

```bash
# Iniciar todos os serviços
docker-compose up -d

# Logs da aplicação
docker-compose logs -f app

# Parar serviços
docker-compose down
```

### Serviços Disponíveis

- **PostgreSQL**: localhost:5432
- **pgAdmin**: localhost:8080 (<EMAIL> / admin123)

## 🔧 Configuração

### Variáveis de Ambiente (.env)

```env
# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=manga_user
DATABASE_PASSWORD=manga_password
DATABASE_NAME=manga_tracker

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# App
PORT=3000
NODE_ENV=development
```

## 📈 Roadmap

- [ ] Sistema completo de anotações
- [ ] Rankings e listas compartilhadas
- [ ] Sistema de recomendações
- [ ] API de integração com sites de mangás
- [ ] Sistema de notificações
- [ ] App mobile (React Native)
- [ ] Sistema de amigos e social

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

## 👨‍💻 Autor

Desenvolvido com ❤️ para a comunidade de leitores de mangás, manhwas e manhuas.

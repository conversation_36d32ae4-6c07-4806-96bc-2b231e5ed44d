import { ApiProperty } from "@nestjs/swagger";
import { IsEmail, IsNotEmpty, IsOptional, IsString, IsUrl, <PERSON>Length, MinLength } from "class-validator";

export class RegisterDto {
	@ApiProperty({
		description: "Nome de usuário único",
		example: "otaku_reader",
		minLength: 3,
		maxLength: 50,
	})
	@IsNotEmpty()
	@IsString()
	@MinLength(3)
	@MaxLength(50)
	username: string;

	@ApiProperty({
		description: "Email do usuário",
		example: "<EMAIL>",
	})
	@IsNotEmpty()
	@IsEmail()
	email: string;

	@ApiProperty({
		description: "Senha do usuário",
		example: "MinhaSenh@123",
		minLength: 6,
	})
	@IsNotEmpty()
	@IsString()
	@MinLength(6)
	password: string;

	@ApiProperty({
		description: "Nome completo do usuário",
		example: "<PERSON>",
		maxLength: 100,
		required: false,
	})
	@IsOptional()
	@IsString()
	@MaxLength(100)
	fullName?: string;

	@ApiProperty({
		description: "Avatar do usuário (URL)",
		example: "https://example.com/avatar.jpg",
		required: false,
	})
	@IsOptional()
	@IsUrl()
	avatar?: string;
}

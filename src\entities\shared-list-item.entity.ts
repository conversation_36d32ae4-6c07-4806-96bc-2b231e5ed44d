import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn, Index } from "typeorm";
import { ApiProperty } from "@nestjs/swagger";
import { SharedList } from "./shared-list.entity";
import { Work } from "./work.entity";

@Entity("shared_list_items")
@Index(["sharedListId", "workId"], { unique: true })
export class SharedListItem {
	@ApiProperty({
		description: "ID único do item da lista compartilhada",
		example: 1,
	})
	@PrimaryGeneratedColumn()
	id: number;

	@ApiProperty({
		description: "ID da lista compartilhada",
		example: 1,
	})
	@Column()
	sharedListId: number;

	@ApiProperty({
		description: "ID da obra",
		example: 1,
	})
	@Column()
	workId: number;

	@ApiProperty({
		description: "Comentário sobre a recomendação",
		example: "Uma obra-prima que mudou minha perspectiva sobre mangás",
	})
	@Column({ type: "text", nullable: true })
	comment?: string;

	@ApiProperty({
		description: "Ordem na lista",
		example: 1,
	})
	@Column()
	order: number;

	@ApiProperty({
		description: "Data de adição",
	})
	@CreateDateColumn()
	createdAt: Date;

	@ManyToOne(() => SharedList, sharedList => sharedList.items, { onDelete: "CASCADE" })
	@JoinColumn({ name: "sharedListId" })
	sharedList: SharedList;

	@ManyToOne(() => Work, { onDelete: "CASCADE" })
	@JoinColumn({ name: "workId" })
	work: Work;
}

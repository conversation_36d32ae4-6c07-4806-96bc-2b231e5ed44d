import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";
import databaseConfig from "./config/database.config";
import { AuthModule } from "./modules/auth/auth.module";
import { WorksModule } from "./modules/works/works.module";
import { UserWorksModule } from "./modules/user-works/user-works.module";

@Module({
	imports: [
		// Configuração global
		ConfigModule.forRoot({
			isGlobal: true,
			load: [databaseConfig],
			envFilePath: ".env",
		}),

		// Configuração do banco de dados
		TypeOrmModule.forRootAsync({
			useFactory: databaseConfig,
		}),

		// Módulos da aplicação
		AuthModule,
		WorksModule,
		UserWorksModule,
	],
	controllers: [],
	providers: [],
})
export class AppModule {}

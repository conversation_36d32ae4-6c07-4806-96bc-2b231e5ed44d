import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UserWorksService } from "./user-works.service";
import { UserWorksController } from "./user-works.controller";
import { UserWork, Work } from "../../entities";
import { WorksModule } from "../works/works.module";

@Module({
	imports: [TypeOrmModule.forFeature([UserWork, Work]), WorksModule],
	controllers: [UserWorksController],
	providers: [UserWorksService],
	exports: [UserWorksService],
})
export class UserWorksModule {}

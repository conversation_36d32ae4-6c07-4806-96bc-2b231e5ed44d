import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, <PERSON>umn, CreateDateColumn, UpdateDateColumn, OneToMany } from "typeorm";
import { ApiProperty } from "@nestjs/swagger";
import { Note } from "./note.entity";
import { Ranking } from "./ranking.entity";
import { SharedList } from "./shared-list.entity";
import { UserWork } from "./user-work.entity";

@Entity("users")
export class User {
	@ApiProperty({
		description: "ID único do usuário",
		example: 1,
	})
	@PrimaryGeneratedColumn()
	id: number;

	@ApiProperty({
		description: "Nome de usuário único",
		example: "otaku_reader",
		maxLength: 50,
	})
	@Column({ unique: true, length: 50 })
	username: string;

	@ApiProperty({
		description: "Email do usuário",
		example: "<EMAIL>",
	})
	@Column({ unique: true })
	email: string;

	@Column()
	password: string;

	@ApiProperty({
		description: "Nome completo do usuário",
		example: "<PERSON>",
		maxLength: 100,
	})
	@Column({ length: 100, nullable: true })
	fullName?: string;

	@ApiProperty({
		description: "Avatar do usuário (URL)",
		example: "https://example.com/avatar.jpg",
	})
	@Column({ nullable: true })
	avatar?: string;

	@ApiProperty({
		description: "Biografia do usuário",
		example: "Leitor apaixonado por mangás de ação e romance",
	})
	@Column({ type: "text", nullable: true })
	bio?: string;

	@ApiProperty({
		description: "Se o perfil é público",
		example: true,
	})
	@Column({ default: true })
	isPublic: boolean;

	@ApiProperty({
		description: "Data de criação do usuário",
	})
	@CreateDateColumn()
	createdAt: Date;

	@ApiProperty({
		description: "Data de última atualização",
	})
	@UpdateDateColumn()
	updatedAt: Date;

	// Relacionamentos
	@OneToMany(() => UserWork, userWork => userWork.user)
	userWorks: UserWork[];

	@OneToMany(() => Note, note => note.user)
	notes: Note[];

	@OneToMany(() => Ranking, ranking => ranking.user)
	rankings: Ranking[];

	@OneToMany(() => SharedList, sharedList => sharedList.owner)
	sharedLists: SharedList[];
}

import { ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsOptional, IsPositive, Max, Min } from "class-validator";

export class PaginationDto {
	@ApiPropertyOptional({
		description: "Número da página",
		example: 1,
		minimum: 1,
		default: 1,
	})
	@IsOptional()
	@Type(() => Number)
	@IsPositive()
	page?: number = 1;

	@ApiPropertyOptional({
		description: "Número de itens por página",
		example: 10,
		minimum: 1,
		maximum: 100,
		default: 10,
	})
	@IsOptional()
	@Type(() => Number)
	@Min(1)
	@Max(100)
	limit?: number = 10;
}

export class PaginationResponseDto<T> {
	@ApiPropertyOptional({
		description: "Dados da página atual",
	})
	data: T[];

	@ApiPropertyOptional({
		description: "Informações de paginação",
	})
	meta: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
		hasNext: boolean;
		hasPrev: boolean;
	};
}

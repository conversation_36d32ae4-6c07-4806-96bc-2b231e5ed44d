import { Injectable, Logger } from "@nestjs/common";
import { Cron, CronExpression } from "@nestjs/schedule";
import { RatingMetricsService } from "../services/rating-metrics.service";

@Injectable()
export class RatingMetricsTask {
	private readonly logger = new Logger(RatingMetricsTask.name);

	constructor(private readonly ratingMetricsService: RatingMetricsService) {}

	/**
	 * Atualiza métricas de rating a cada 6 horas
	 */
	@Cron(CronExpression.EVERY_6_HOURS)
	async updateAllMetrics() {
		this.logger.log("Iniciando atualização automática de métricas de rating");

		try {
			await this.ratingMetricsService.updateAllWorksMetrics(50); // Lotes de 50
			this.logger.log("Atualização automática de métricas concluída com sucesso");
		} catch (error) {
			this.logger.error("Erro na atualização automática de métricas:", error);
		}
	}

	/**
	 * Limpa métricas antigas uma vez por semana (domingo às 2h)
	 */
	@Cron(CronExpression.EVERY_WEEK)
	async cleanupOldMetrics() {
		this.logger.log("Iniciando limpeza de métricas antigas");

		try {
			await this.ratingMetricsService.cleanupOldMetrics(50); // Manter últimas 50 por obra
			this.logger.log("Limpeza de métricas antigas concluída com sucesso");
		} catch (error) {
			this.logger.error("Erro na limpeza de métricas antigas:", error);
		}
	}

	/**
	 * Método manual para forçar atualização de todas as métricas
	 */
	async forceUpdateAllMetrics(): Promise<void> {
		this.logger.log("Forçando atualização manual de todas as métricas");

		try {
			await this.ratingMetricsService.updateAllWorksMetrics(25); // Lotes menores para execução manual
			this.logger.log("Atualização manual de métricas concluída com sucesso");
		} catch (error) {
			this.logger.error("Erro na atualização manual de métricas:", error);
			throw error;
		}
	}

	/**
	 * Método para atualizar métricas de obras específicas
	 */
	async updateSpecificWorks(workIds: number[]): Promise<void> {
		this.logger.log(`Atualizando métricas para ${workIds.length} obras específicas`);

		try {
			const promises = workIds.map(workId =>
				this.ratingMetricsService.updateWorkRatingMetrics(workId).catch(error => {
					this.logger.error(`Erro ao atualizar métricas da obra ${workId}:`, error);
				})
			);
			await Promise.all(promises);
			this.logger.log("Atualização de obras específicas concluída");
		} catch (error) {
			this.logger.error("Erro na atualização de obras específicas:", error);
			throw error;
		}
	}

	/**
	 * Gera relatório de status das métricas
	 */
	async generateMetricsReport(): Promise<{
		totalWorks: number;
		worksWithMetrics: number;
		averageQualityScore: number;
		topTrendingWorks: Array<{ workId: number; trendingScore: number }>;
		systemHealth: string;
	}> {
		this.logger.log("Gerando relatório de métricas");

		try {
			const stats = await this.ratingMetricsService.getSystemRatingStats();

			const systemHealth = this.calculateSystemHealth(stats);

			return {
				totalWorks: stats.totalWorks,
				worksWithMetrics: stats.totalWorks, // Assumindo que todas as obras têm métricas
				averageQualityScore: stats.averageConfidence,
				topTrendingWorks: stats.trendingWorks.map(w => ({
					workId: w.workId,
					trendingScore: w.trendingScore,
				})),
				systemHealth,
			};
		} catch (error) {
			this.logger.error("Erro ao gerar relatório de métricas:", error);
			throw error;
		}
	}

	/**
	 * Calcula a saúde geral do sistema baseado nas métricas
	 */
	private calculateSystemHealth(stats: any): string {
		const avgRating = stats.averageRating;
		const avgConfidence = stats.averageConfidence;
		const totalRatings = stats.totalRatings;

		let healthScore = 0;

		// Score baseado na média geral (0-40 pontos)
		if (avgRating >= 7.5) healthScore += 40;
		else if (avgRating >= 6.5) healthScore += 30;
		else if (avgRating >= 5.5) healthScore += 20;
		else healthScore += 10;

		// Score baseado na confiança (0-30 pontos)
		if (avgConfidence >= 80) healthScore += 30;
		else if (avgConfidence >= 60) healthScore += 20;
		else if (avgConfidence >= 40) healthScore += 10;

		// Score baseado no volume de avaliações (0-30 pontos)
		if (totalRatings >= 10000) healthScore += 30;
		else if (totalRatings >= 5000) healthScore += 20;
		else if (totalRatings >= 1000) healthScore += 10;

		if (healthScore >= 90) return "Excelente";
		if (healthScore >= 70) return "Bom";
		if (healthScore >= 50) return "Regular";
		return "Precisa Atenção";
	}
}

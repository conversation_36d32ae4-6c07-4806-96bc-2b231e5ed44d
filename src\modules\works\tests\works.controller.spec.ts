import { Test, TestingModule } from "@nestjs/testing";
import { WorksController } from "../works.controller";
import { WorksService } from "../works.service";
import { Work, WorkType, WorkStatus } from "../../../entities";
import { JwtAuthGuard } from "../../auth/guards/jwt-auth.guard";

describe("WorksController", () => {
	let controller: WorksController;
	let service: WorksService;

	const mockWork: Work = {
		id: 1,
		title: "Test Manga",
		type: WorkType.MANGA,
		author: "Test Author",
		status: WorkStatus.ONGOING,
		averageRating: 8.5,
		totalRatings: 100,
		createdAt: new Date(),
		updatedAt: new Date(),
		userWorks: [],
		notes: [],
	};

	const mockWorksService = {
		create: jest.fn(),
		findAll: jest.fn(),
		findOne: jest.fn(),
		update: jest.fn(),
		remove: jest.fn(),
		getPopular: jest.fn(),
		getByGenre: jest.fn(),
		getRandomWorks: jest.fn(),
		getWorkStats: jest.fn(),
		findAllAdvanced: jest.fn(),
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [WorksController],
			providers: [
				{
					provide: WorksService,
					useValue: mockWorksService,
				},
			],
		})
			.overrideGuard(JwtAuthGuard)
			.useValue({ canActivate: () => true })
			.compile();

		controller = module.get<WorksController>(WorksController);
		service = module.get<WorksService>(WorksService);
	});

	it("should be defined", () => {
		expect(controller).toBeDefined();
	});

	describe("create", () => {
		it("should create a work", async () => {
			const createWorkDto = {
				title: "New Manga",
				type: WorkType.MANGA,
				author: "New Author",
			};

			mockWorksService.create.mockResolvedValue(mockWork);

			const result = await controller.create(createWorkDto as any);

			expect(result).toEqual(mockWork);
			expect(mockWorksService.create).toHaveBeenCalledWith(createWorkDto);
		});
	});

	describe("findAll", () => {
		it("should return paginated works", async () => {
			const paginationDto = { page: 1, limit: 10 };
			const filters = { search: "test" };
			const expectedResult = {
				data: [mockWork],
				meta: {
					page: 1,
					limit: 10,
					total: 1,
					totalPages: 1,
					hasNext: false,
					hasPrev: false,
				},
			};

			mockWorksService.findAll.mockResolvedValue(expectedResult);

			const result = await controller.findAll(paginationDto, filters.search);

			expect(result).toEqual(expectedResult);
			expect(mockWorksService.findAll).toHaveBeenCalledWith(paginationDto, {
				search: filters.search,
				type: undefined,
				status: undefined,
				genre: undefined,
				author: undefined,
			});
		});
	});

	describe("findOne", () => {
		it("should return a work by id", async () => {
			mockWorksService.findOne.mockResolvedValue(mockWork);

			const result = await controller.findOne(1);

			expect(result).toEqual(mockWork);
			expect(mockWorksService.findOne).toHaveBeenCalledWith(1);
		});
	});

	describe("getPopular", () => {
		it("should return popular works", async () => {
			const popularWorks = [mockWork];
			mockWorksService.getPopular.mockResolvedValue(popularWorks);

			const result = await controller.getPopular(10);

			expect(result).toEqual(popularWorks);
			expect(mockWorksService.getPopular).toHaveBeenCalledWith(10);
		});
	});

	describe("getByGenre", () => {
		it("should return works by genre", async () => {
			const genre = "Ação";
			const works = [mockWork];
			mockWorksService.getByGenre.mockResolvedValue(works);

			const result = await controller.getByGenre(genre, 10);

			expect(result).toEqual(works);
			expect(mockWorksService.getByGenre).toHaveBeenCalledWith(genre, 10);
		});
	});

	describe("getRandomWorks", () => {
		it("should return random works", async () => {
			const randomWorks = [mockWork];
			mockWorksService.getRandomWorks.mockResolvedValue(randomWorks);

			const result = await controller.getRandomWorks(5);

			expect(result).toEqual(randomWorks);
			expect(mockWorksService.getRandomWorks).toHaveBeenCalledWith(5);
		});
	});

	describe("update", () => {
		it("should update a work", async () => {
			const updateWorkDto = { title: "Updated Manga" };
			const updatedWork = { ...mockWork, title: "Updated Manga" };

			mockWorksService.update.mockResolvedValue(updatedWork);

			const result = await controller.update(1, updateWorkDto);

			expect(result).toEqual(updatedWork);
			expect(mockWorksService.update).toHaveBeenCalledWith(1, updateWorkDto);
		});
	});

	describe("remove", () => {
		it("should remove a work", async () => {
			mockWorksService.remove.mockResolvedValue(undefined);

			await controller.remove(1);

			expect(mockWorksService.remove).toHaveBeenCalledWith(1);
		});
	});
});

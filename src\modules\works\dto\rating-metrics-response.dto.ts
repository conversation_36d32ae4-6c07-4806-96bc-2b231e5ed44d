import { ApiProperty } from "@nestjs/swagger";
import { Work, RatingMetrics } from "../../../entities";

export class RatingMetricsResponseDto {
	@ApiProperty({
		description: "<PERSON><PERSON> da obra",
		type: Work,
	})
	work: Work;
	@ApiProperty({
		description: "Métricas mais recentes",
		type: RatingMetrics,
		nullable: true,
	})
	latestMetrics: RatingMetrics | null;
	@ApiProperty({
		description: "Histórico de métricas",
		type: [RatingMetrics],
	})
	metricsHistory: RatingMetrics[];
	@ApiProperty({
		description: "Análise de qualidade",
		example: {
			category: "Excelente",
			score: 85.5,
			reliability: 78.2,
			isTrending: true,
			popularityScore: 92.1,
		},
	})
	analysis: {
		category: string;
		score: number;
		reliability: number;
		isTrending: boolean;
		popularityScore: number;
	};
}

export class SystemRatingStatsDto {
	@ApiProperty({
		description: "Total de obras no sistema",
		example: 1250,
	})
	totalWorks: number;

	@ApiProperty({
		description: "Total de avaliações",
		example: 45000,
	})
	totalRatings: number;
	@ApiProperty({
		description: "Média geral de rating",
		example: 7.8,
	})
	averageRating: number;
	@ApiProperty({
		description: "Confiança média do sistema",
		example: 82.5,
	})
	averageConfidence: number;
	@ApiProperty({
		description: "Top 10 obras por qualidade",
		example: [
			{ workId: 1, title: "One Piece", qualityScore: 95.2 },
			{ workId: 2, title: "Attack on Titan", qualityScore: 94.8 },
		],
	})
	topQualityWorks: Array<{
		workId: number;
		title: string;
		qualityScore: number;
	}>;
	@ApiProperty({
		description: "Top 10 obras em alta",
		example: [
			{ workId: 3, title: "Demon Slayer", trendingScore: 88.5 },
			{ workId: 4, title: "Jujutsu Kaisen", trendingScore: 87.2 },
		],
	})
	trendingWorks: Array<{
		workId: number;
		title: string;
		trendingScore: number;
	}>;
	@ApiProperty({
		description: "Saúde geral do sistema",
		example: "Excelente",
		enum: ["Excelente", "Bom", "Regular", "Precisa Atenção"],
	})
	systemHealth: string;
}

export class RatingDistributionDto {
	@ApiProperty({
		description: "Distribuição de notas (índices 0-9 representam notas 1-10)",
		example: [5, 8, 12, 18, 25, 30, 35, 28, 20, 15],
		type: [Number],
	})
	distribution: number[];
	@ApiProperty({
		description: "Percentuais de cada nota",
		example: [2.5, 4.0, 6.0, 9.0, 12.5, 15.0, 17.5, 14.0, 10.0, 7.5],
		type: [Number],
	})
	percentages: number[];
	@ApiProperty({
		description: "Nota mais comum",
		example: 7,
	})
	mode: number;
	@ApiProperty({
		description: "Concentração de notas altas (7-10)",
		example: 68.5,
	})
	highRatingPercentage: number;
}

export class TrendAnalysisDto {
	@ApiProperty({
		description: "Tendência nos últimos 30 dias (-1 a 1)",
		example: 0.15,
	})
	trend30Days: number;

	@ApiProperty({
		description: "Velocidade de mudança do rating",
		example: 0.05,
	})
	ratingVelocity: number;

	@ApiProperty({
		description: "Avaliações recentes (7 dias)",
		example: 12,
	})
	recentRatings7d: number;

	@ApiProperty({
		description: "Avaliações recentes (30 dias)",
		example: 45,
	})
	recentRatings30d: number;

	@ApiProperty({
		description: "Score de trending",
		example: 75.8,
	})
	trendingScore: number;

	@ApiProperty({
		description: "Interpretação da tendência",
		example: "Em alta",
		enum: ["Em alta", "Estável", "Em declínio", "Sem dados suficientes"],
	})
	interpretation: string;
}

export class QualityAnalysisDto {
	@ApiProperty({
		description: "Score de qualidade geral",
		example: 85.5,
	})
	qualityScore: number;

	@ApiProperty({
		description: "Categoria de qualidade",
		example: "Excelente",
		enum: ["Excepcional", "Excelente", "Muito Bom", "Bom", "Regular", "Baixo"],
	})
	category: string;

	@ApiProperty({
		description: "Score de confiabilidade",
		example: 78.2,
	})
	reliabilityScore: number;

	@ApiProperty({
		description: "Fatores que contribuem para a qualidade",
		example: {
			rating: 85,
			consistency: 75,
			popularity: 90,
			activity: 70,
		},
	})
	factors: {
		rating: number;
		consistency: number;
		popularity: number;
		activity: number;
	};

	@ApiProperty({
		description: "Recomendações para melhoria",
		example: ["Aumentar número de avaliações", "Manter qualidade consistente"],
		type: [String],
	})
	recommendations: string[];
}

import { Injectable, UnauthorizedException, ConflictException, BadRequestException } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import * as bcrypt from "bcryptjs";
import { User } from "../../entities";
import { RegisterDto } from "./dto/register.dto";
import { LoginDto } from "./dto/login.dto";

@Injectable()
export class AuthService {
	constructor(
		@InjectRepository(User)
		private readonly userRepository: Repository<User>,
		private readonly jwtService: JwtService
	) {}

	async register(registerDto: RegisterDto): Promise<{ user: Omit<User, "password">; token: string }> {
		const { username, email, password, ...userData } = registerDto;

		// Verificar se usuário já existe
		const existingUser = await this.userRepository.findOne({
			where: [{ username }, { email }],
		});

		if (existingUser) {
			if (existingUser.username === username) {
				throw new ConflictException("Nome de usuário já está em uso");
			}
			if (existingUser.email === email) {
				throw new ConflictException("Email já está em uso");
			}
		}

		// Hash da senha
		const saltRounds = 12;
		const hashedPassword = await bcrypt.hash(password, saltRounds);

		// Criar usuário
		const user = this.userRepository.create({
			username,
			email,
			password: hashedPassword,
			...userData,
		});

		const savedUser = await this.userRepository.save(user);

		// Gerar token
		const token = this.generateToken(savedUser);

		// Remover senha do retorno
		const { password: _, ...userWithoutPassword } = savedUser;

		return {
			user: userWithoutPassword,
			token,
		};
	}

	async login(loginDto: LoginDto): Promise<{ user: Omit<User, "password">; token: string }> {
		const { usernameOrEmail, password } = loginDto;

		// Buscar usuário por username ou email
		const user = await this.userRepository.findOne({
			where: [{ username: usernameOrEmail }, { email: usernameOrEmail }],
		});

		if (!user) {
			throw new UnauthorizedException("Credenciais inválidas");
		}

		// Verificar senha
		const isPasswordValid = await bcrypt.compare(password, user.password);
		if (!isPasswordValid) {
			throw new UnauthorizedException("Credenciais inválidas");
		}

		// Gerar token
		const token = this.generateToken(user);

		// Remover senha do retorno
		const { password: _, ...userWithoutPassword } = user;

		return {
			user: userWithoutPassword,
			token,
		};
	}

	async validateUser(userId: number): Promise<User | null> {
		return this.userRepository.findOne({ where: { id: userId } });
	}

	private generateToken(user: User): string {
		const payload = {
			sub: user.id,
			username: user.username,
			email: user.email,
		};

		return this.jwtService.sign(payload);
	}
}

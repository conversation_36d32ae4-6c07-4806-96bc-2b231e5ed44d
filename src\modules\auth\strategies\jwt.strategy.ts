import { Injectable, UnauthorizedException } from "@nestjs/common";
import { PassportStrategy } from "@nestjs/passport";
import { ExtractJwt, Strategy } from "passport-jwt";
import { AuthService } from "../auth.service";

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
	constructor(private readonly authService: AuthService) {
		super({
			jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
			ignoreExpiration: false,
			secretOrKey: process.env.JWT_SECRET || "your-super-secret-jwt-key-change-this-in-production",
		});
	}

	async validate(payload: any) {
		const user = await this.authService.validateUser(payload.sub);
		if (!user) {
			throw new UnauthorizedException("Usuário não encontrado");
		}
		return user;
	}
}

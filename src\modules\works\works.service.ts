import { Injectable, NotFoundException, ConflictException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Work, WorkType, WorkStatus } from "../../entities";
import { CreateWorkDto } from "./dto/create-work.dto";
import { UpdateWorkDto } from "./dto/update-work.dto";
import { PaginationDto, PaginationResponseDto } from "../../common/dto/pagination.dto";
import { WorkFiltersDto, SortBy, SortOrder } from "./dto/work-filters.dto";
import { RatingMetricsService } from "./services/rating-metrics.service";

@Injectable()
export class WorksService {
	constructor(
		@InjectRepository(Work)
		private readonly workRepository: Repository<Work>,
		private readonly ratingMetricsService: RatingMetricsService
	) {}

	async create(createWorkDto: CreateWorkDto): Promise<Work> {
		// Verificar se já existe uma obra com o mesmo título e tipo
		const existingWork = await this.workRepository.findOne({
			where: {
				title: createWorkDto.title,
				type: createWorkDto.type,
			},
		});

		if (existingWork) {
			throw new ConflictException("Já existe uma obra com este título e tipo");
		}

		const work = this.workRepository.create(createWorkDto);
		return this.workRepository.save(work);
	}

	async findAll(
		paginationDto: PaginationDto,
		filters?: {
			search?: string;
			type?: WorkType;
			status?: WorkStatus;
			genre?: string;
			author?: string;
		}
	): Promise<PaginationResponseDto<Work>> {
		const { page = 1, limit = 10 } = paginationDto;
		const skip = (page - 1) * limit;

		const queryBuilder = this.workRepository.createQueryBuilder("work");

		// Aplicar filtros
		if (filters?.search) {
			queryBuilder.andWhere("(work.title ILIKE :search OR work.alternativeTitle ILIKE :search OR work.author ILIKE :search)", {
				search: `%${filters.search}%`,
			});
		}

		if (filters?.type) {
			queryBuilder.andWhere("work.type = :type", { type: filters.type });
		}

		if (filters?.status) {
			queryBuilder.andWhere("work.status = :status", { status: filters.status });
		}

		if (filters?.genre) {
			queryBuilder.andWhere(":genre = ANY(work.genres)", { genre: filters.genre });
		}

		if (filters?.author) {
			queryBuilder.andWhere("work.author ILIKE :author", { author: `%${filters.author}%` });
		}

		// Ordenação padrão por nota média e total de avaliações
		queryBuilder.orderBy("work.averageRating", "DESC");
		queryBuilder.addOrderBy("work.totalRatings", "DESC");
		queryBuilder.addOrderBy("work.createdAt", "DESC");

		// Paginação
		queryBuilder.skip(skip).take(limit);

		const [data, total] = await queryBuilder.getManyAndCount();

		const totalPages = Math.ceil(total / limit);

		return {
			data,
			meta: {
				page,
				limit,
				total,
				totalPages,
				hasNext: page < totalPages,
				hasPrev: page > 1,
			},
		};
	}

	async findOne(id: number): Promise<Work> {
		const work = await this.workRepository.findOne({
			where: { id },
			relations: ["userWorks", "notes"],
		});

		if (!work) {
			throw new NotFoundException("Obra não encontrada");
		}

		return work;
	}

	async update(id: number, updateWorkDto: UpdateWorkDto): Promise<Work> {
		const work = await this.findOne(id);

		// Se estiver atualizando título ou tipo, verificar conflitos
		if (updateWorkDto.title || updateWorkDto.type) {
			const title = updateWorkDto.title || work.title;
			const type = updateWorkDto.type || work.type;

			const existingWork = await this.workRepository.findOne({
				where: { title, type },
			});

			if (existingWork && existingWork.id !== id) {
				throw new ConflictException("Já existe uma obra com este título e tipo");
			}
		}

		Object.assign(work, updateWorkDto);
		return this.workRepository.save(work);
	}

	async remove(id: number): Promise<void> {
		const work = await this.findOne(id);
		await this.workRepository.remove(work);
	}

	async updateRating(workId: number): Promise<void> {
		// Usar o novo sistema de métricas avançadas
		await this.ratingMetricsService.updateWorkRatingMetrics(workId);
	}

	async getPopular(limit: number = 10): Promise<Work[]> {
		return this.workRepository.find({
			order: {
				weightedRating: "DESC",
				qualityScore: "DESC",
				totalRatings: "DESC",
			},
			take: limit,
		});
	}

	/**
	 * Obtém obras em alta baseado no novo score de trending
	 */
	async getTrendingWorksAdvanced(limit: number = 10): Promise<Work[]> {
		return this.workRepository.find({
			order: {
				trendingScore: "DESC",
				recentRatingsCount: "DESC",
			},
			take: limit,
		});
	}

	/**
	 * Obtém obras com melhor qualidade
	 */
	async getHighQualityWorks(limit: number = 10): Promise<Work[]> {
		return this.workRepository.find({
			where: {
				totalRatings: { $gte: 50 } as any, // Mínimo de 50 avaliações
			},
			order: {
				qualityScore: "DESC",
				weightedRating: "DESC",
			},
			take: limit,
		});
	}

	/**
	 * Obtém métricas detalhadas de uma obra
	 */
	async getWorkMetrics(workId: number) {
		const work = await this.findOne(workId);
		const latestMetrics = await this.ratingMetricsService.getLatestMetrics(workId);
		const metricsHistory = await this.ratingMetricsService.getMetricsHistory(workId, 10);

		return {
			work,
			latestMetrics,
			metricsHistory,
		};
	}

	async getByGenre(genre: string, limit: number = 10): Promise<Work[]> {
		return this.workRepository
			.createQueryBuilder("work")
			.where(":genre = ANY(work.genres)", { genre })
			.orderBy("work.averageRating", "DESC")
			.limit(limit)
			.getMany();
	}

	async getRandomWorks(limit: number = 5): Promise<Work[]> {
		return this.workRepository.createQueryBuilder("work").orderBy("RANDOM()").limit(limit).getMany();
	}

	async getWorkStats(): Promise<any> {
		const totalWorks = await this.workRepository.count();

		const worksByType = await this.workRepository
			.createQueryBuilder("work")
			.select("work.type", "type")
			.addSelect("COUNT(*)", "count")
			.groupBy("work.type")
			.getRawMany();

		const worksByStatus = await this.workRepository
			.createQueryBuilder("work")
			.select("work.status", "status")
			.addSelect("COUNT(*)", "count")
			.groupBy("work.status")
			.getRawMany();

		return {
			totalWorks,
			worksByType: worksByType.reduce((acc, curr) => {
				acc[curr.type] = parseInt(curr.count);
				return acc;
			}, {}),
			worksByStatus: worksByStatus.reduce((acc, curr) => {
				acc[curr.status] = parseInt(curr.count);
				return acc;
			}, {}),
		};
	}

	async findAllAdvanced(paginationDto: PaginationDto, filters: WorkFiltersDto): Promise<PaginationResponseDto<Work>> {
		const { page = 1, limit = 10 } = paginationDto;
		const skip = (page - 1) * limit;

		const queryBuilder = this.workRepository.createQueryBuilder("work");

		if (filters.search) {
			queryBuilder.andWhere("(work.title ILIKE :search OR work.alternativeTitle ILIKE :search OR work.author ILIKE :search)", {
				search: `%${filters.search}%`,
			});
		}

		if (filters.type) {
			queryBuilder.andWhere("work.type = :type", { type: filters.type });
		}

		if (filters.status) {
			queryBuilder.andWhere("work.status = :status", { status: filters.status });
		}

		if (filters.author) {
			queryBuilder.andWhere("work.author ILIKE :author", { author: `%${filters.author}%` });
		}

		if (filters.genre) {
			queryBuilder.andWhere(":genre = ANY(work.genres)", { genre: filters.genre });
		}

		if (filters.minRating) {
			queryBuilder.andWhere("work.averageRating >= :minRating", { minRating: filters.minRating });
		}

		if (filters.minYear) {
			queryBuilder.andWhere("work.publicationYear >= :minYear", { minYear: filters.minYear });
		}

		if (filters.maxYear) {
			queryBuilder.andWhere("work.publicationYear <= :maxYear", { maxYear: filters.maxYear });
		}

		const sortBy = filters.sortBy || SortBy.AVERAGE_RATING;
		const sortOrder = filters.sortOrder || SortOrder.DESC;

		queryBuilder.orderBy(`work.${sortBy}`, sortOrder);

		if (sortBy !== SortBy.AVERAGE_RATING) {
			queryBuilder.addOrderBy("work.averageRating", "DESC");
		}

		queryBuilder.skip(skip).take(limit);

		const [data, total] = await queryBuilder.getManyAndCount();
		const totalPages = Math.ceil(total / limit);

		return {
			data,
			meta: {
				page,
				limit,
				total,
				totalPages,
				hasNext: page < totalPages,
				hasPrev: page > 1,
			},
		};
	}

	async getSimilarWorks(workId: number, limit: number = 10): Promise<Work[]> {
		const work = await this.findOne(workId);

		const queryBuilder = this.workRepository.createQueryBuilder("work");

		// Buscar obras similares baseadas em:
		// 1. Mesmo tipo
		// 2. Gêneros em comum
		// 3. Mesmo autor (peso menor)

		let similarityScore = "0";

		// Peso por tipo (30%)
		similarityScore += ` + CASE WHEN work.type = '${work.type}' THEN 30 ELSE 0 END`;

		// Peso por gêneros em comum (50%)
		if (work.genres && work.genres.length > 0) {
			const genreMatches = work.genres.map(genre => `CASE WHEN '${genre}' = ANY(work.genres) THEN 1 ELSE 0 END`).join(" + ");
			similarityScore += ` + (${genreMatches}) * ${50 / work.genres.length}`;
		}

		// Peso por mesmo autor (20%)
		similarityScore += ` + CASE WHEN work.author = '${work.author}' THEN 20 ELSE 0 END`;

		queryBuilder.where("work.id != :workId", { workId }).orderBy(`(${similarityScore})`, "DESC").addOrderBy("work.averageRating", "DESC").limit(limit);

		return queryBuilder.getMany();
	}

	async getTrendingWorks(days: number = 7, limit: number = 10): Promise<Work[]> {
		// Obras que ganharam mais avaliações nos últimos dias
		const sinceDate = new Date();
		sinceDate.setDate(sinceDate.getDate() - days);

		return this.workRepository
			.createQueryBuilder("work")
			.leftJoin("work.userWorks", "userWork")
			.where("userWork.updatedAt >= :sinceDate", { sinceDate })
			.groupBy("work.id")
			.orderBy("COUNT(userWork.id)", "DESC")
			.addOrderBy("work.averageRating", "DESC")
			.limit(limit)
			.getMany();
	}

	async getWorksByDecade(): Promise<Array<{ decade: string; count: number; averageRating: number }>> {
		const result = await this.workRepository
			.createQueryBuilder("work")
			.select("FLOOR(work.publicationYear / 10) * 10", "decade")
			.addSelect("COUNT(*)", "count")
			.addSelect("AVG(work.averageRating)", "averageRating")
			.where("work.publicationYear IS NOT NULL")
			.groupBy("decade")
			.orderBy("decade", "DESC")
			.getRawMany();

		return result.map(row => ({
			decade: `${row.decade}s`,
			count: parseInt(row.count),
			averageRating: parseFloat(row.averageRating) || 0,
		}));
	}

	async getTopAuthors(limit: number = 20): Promise<Array<{ author: string; worksCount: number; averageRating: number }>> {
		const result = await this.workRepository
			.createQueryBuilder("work")
			.select("work.author", "author")
			.addSelect("COUNT(*)", "worksCount")
			.addSelect("AVG(work.averageRating)", "averageRating")
			.groupBy("work.author")
			.having("COUNT(*) >= 2") // Autores com pelo menos 2 obras
			.orderBy("averageRating", "DESC")
			.addOrderBy("worksCount", "DESC")
			.limit(limit)
			.getRawMany();

		return result.map(row => ({
			author: row.author,
			worksCount: parseInt(row.worksCount),
			averageRating: parseFloat(row.averageRating) || 0,
		}));
	}

	async getGenreStatistics(): Promise<Array<{ genre: string; count: number; averageRating: number }>> {
		// Esta é uma query mais complexa que precisa de SQL raw
		const result = await this.workRepository.query(`
			SELECT 
				genre,
				COUNT(*) as count,
				AVG(average_rating) as average_rating
			FROM (
				SELECT 
					UNNEST(genres) as genre,
					average_rating
				FROM works 
				WHERE genres IS NOT NULL
			) as genre_works
			GROUP BY genre
			HAVING COUNT(*) >= 3
			ORDER BY count DESC, average_rating DESC
		`);

		return result.map(row => ({
			genre: row.genre,
			count: parseInt(row.count),
			averageRating: parseFloat(row.average_rating) || 0,
		}));
	}

	async searchWorksFuzzy(searchTerm: string, limit: number = 20): Promise<Work[]> {
		// Busca fuzzy usando similarity do PostgreSQL
		return this.workRepository
			.createQueryBuilder("work")
			.where(
				`
				similarity(work.title, :searchTerm) > 0.3 OR
				similarity(work.author, :searchTerm) > 0.3 OR
				work.title ILIKE :searchPattern OR
				work.author ILIKE :searchPattern OR
				work.alternativeTitle ILIKE :searchPattern
			`,
				{
					searchTerm,
					searchPattern: `%${searchTerm}%`,
				}
			)
			.orderBy(
				`
				similarity(work.title, '${searchTerm}') + 
				similarity(work.author, '${searchTerm}') +
				CASE WHEN work.title ILIKE '%${searchTerm}%' THEN 0.5 ELSE 0 END
			`,
				"DESC"
			)
			.addOrderBy("work.averageRating", "DESC")
			.limit(limit)
			.getMany();
	}
}

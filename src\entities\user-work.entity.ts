import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index, OneToMany } from "typeorm";
import { ApiProperty } from "@nestjs/swagger";
import { User } from "./user.entity";
import { Work } from "./work.entity";
import { Note } from ".";

export enum ReadingStatus {
	READING = "reading",
	COMPLETED = "completed",
	ON_HOLD = "on_hold",
	DROPPED = "dropped",
	PLAN_TO_READ = "plan_to_read",
}

@Entity("user_works")
@Index(["userId", "workId"], { unique: true })
export class UserWork {
	@ApiProperty({
		description: "ID único do relacionamento usuário-obra",
		example: 1,
	})
	@PrimaryGeneratedColumn()
	id: number;

	@ApiProperty({
		description: "ID do usuário",
		example: 1,
	})
	@Column()
	userId: number;

	@ApiProperty({
		description: "ID da obra",
		example: 1,
	})
	@Column()
	workId: number;

	@ApiProperty({
		description: "Status de leitura",
		enum: ReadingStatus,
		example: ReadingStatus.READING,
	})
	@Column({
		type: "enum",
		enum: ReadingStatus,
		default: ReadingStatus.PLAN_TO_READ,
	})
	status: ReadingStatus;

	@ApiProperty({
		description: "Capítulo atual",
		example: 1050,
	})
	@Column({ default: 0 })
	currentChapter: number;

	@ApiProperty({
		description: "Nota dada pelo usuário (1-10)",
		example: 9.5,
		minimum: 1,
		maximum: 10,
	})
	@Column({ type: "decimal", precision: 3, scale: 2, nullable: true })
	rating?: number;

	@ApiProperty({
		description: "Se a obra está marcada como favorita",
		example: true,
	})
	@Column({ default: false })
	isFavorite: boolean;

	@ApiProperty({
		description: "Data de início da leitura",
	})
	@Column({ nullable: true })
	startDate?: Date;

	@ApiProperty({
		description: "Data de conclusão da leitura",
	})
	@Column({ nullable: true })
	finishDate?: Date;

	@ApiProperty({
		description: "Número de releituras",
		example: 2,
	})
	@Column({ default: 0 })
	rereadCount: number;

	@ApiProperty({
		description: "Comentário pessoal sobre a obra",
		example: "Uma das melhores obras que já li!",
	})
	@Column({ type: "text", nullable: true })
	personalComment?: string;

	@ApiProperty({
		description: "Tags personalizadas",
		example: ["favorito", "ação", "épico"],
		type: [String],
	})
	@Column("simple-array", { nullable: true })
	tags?: string[];

	@ApiProperty({
		description: "Data de criação",
	})
	@CreateDateColumn()
	createdAt: Date;

	@ApiProperty({
		description: "Data de última atualização",
	})
	@UpdateDateColumn()
	updatedAt: Date;

	// Relacionamentos
	@ManyToOne(() => User, user => user.userWorks, { onDelete: "CASCADE" })
	@JoinColumn({ name: "userId" })
	user: User;

	@ManyToOne(() => Work, work => work.userWorks, { onDelete: "CASCADE" })
	@JoinColumn({ name: "workId" })
	work: Work;

	@OneToMany(() => Note, note => note.userWork)
	notes: Note[];
}

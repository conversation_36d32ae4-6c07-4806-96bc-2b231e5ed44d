import { Work, WorkType, WorkStatus } from "../../../entities";

export class WorksUtils {
	/**
	 * Calcula a similaridade entre dois textos usando distância de Levenshtein
	 */
	static calculateSimilarity(str1: string, str2: string): number {
		if (!str1 || !str2) return 0;

		const len1 = str1.length;
		const len2 = str2.length;

		if (len1 === 0) return len2;
		if (len2 === 0) return len1;

		const matrix = Array(len1 + 1)
			.fill(null)
			.map(() => Array(len2 + 1).fill(null));

		for (let i = 0; i <= len1; i++) {
			matrix[i][0] = i;
		}

		for (let j = 0; j <= len2; j++) {
			matrix[0][j] = j;
		}

		for (let i = 1; i <= len1; i++) {
			for (let j = 1; j <= len2; j++) {
				const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
				matrix[i][j] = Math.min(matrix[i - 1][j] + 1, matrix[i][j - 1] + 1, matrix[i - 1][j - 1] + cost);
			}
		}

		const maxLen = Math.max(len1, len2);
		return (maxLen - matrix[len1][len2]) / maxLen;
	}

	/**
	 * Normaliza uma string para comparação
	 */
	static normalizeString(str: string): string {
		return str
			.toLowerCase()
			.normalize("NFD")
			.replace(/[\u0300-\u036f]/g, "")
			.replace(/[^\w\s]/g, "")
			.trim();
	}

	/**
	 * Calcula o score de popularidade de uma obra (versão legada)
	 */
	static calculatePopularityScore(work: Work): number {
		const ratingWeight = 0.7;
		const countWeight = 0.3;

		const normalizedRating = work.averageRating / 10;
		const normalizedCount = Math.log(work.totalRatings + 1) / Math.log(1000); // Log scale

		return normalizedRating * ratingWeight + normalizedCount * countWeight;
	}

	/**
	 * Calcula score de popularidade avançado usando novas métricas
	 */
	static calculateAdvancedPopularityScore(work: Work): number {
		const qualityWeight = 0.4;
		const trendingWeight = 0.3;
		const ratingWeight = 0.2;
		const activityWeight = 0.1;

		const qualityScore = (work.qualityScore || 0) / 100;
		const trendingScore = (work.trendingScore || 0) / 100;
		const ratingScore = (work.weightedRating || work.averageRating) / 10;
		const activityScore = Math.min((work.recentRatingsCount || 0) / 50, 1);

		return (qualityScore * qualityWeight + trendingScore * trendingWeight + ratingScore * ratingWeight + activityScore * activityWeight) * 100;
	}

	/**
	 * Determina a categoria de qualidade de uma obra
	 */
	static getQualityCategory(work: Work): string {
		const score = work.qualityScore || 0;

		if (score >= 90) return "Excepcional";
		if (score >= 80) return "Excelente";
		if (score >= 70) return "Muito Bom";
		if (score >= 60) return "Bom";
		if (score >= 50) return "Regular";
		return "Baixo";
	}

	/**
	 * Determina se uma obra está em alta
	 */
	static isTrending(work: Work): boolean {
		const trendingThreshold = 70;
		const recentActivityThreshold = 10;

		return (work.trendingScore || 0) >= trendingThreshold && (work.recentRatingsCount || 0) >= recentActivityThreshold;
	}

	/**
	 * Calcula score de confiabilidade baseado na distribuição de ratings
	 */
	static calculateReliabilityScore(work: Work): number {
		if (!work.ratingDistribution || work.totalRatings < 10) return 0;

		const distribution = work.ratingDistribution;
		const total = distribution.reduce((sum, count) => sum + count, 0);

		if (total === 0) return 0;

		// Calcula entropia da distribuição (maior entropia = mais confiável)
		let entropy = 0;
		for (const count of distribution) {
			if (count > 0) {
				const probability = count / total;
				entropy -= probability * Math.log2(probability);
			}
		}

		// Normaliza entropia (máximo teórico é log2(10) ≈ 3.32)
		const maxEntropy = Math.log2(10);
		const normalizedEntropy = entropy / maxEntropy;

		// Combina com número de avaliações
		const countScore = Math.min(work.totalRatings / 100, 1);

		return (normalizedEntropy * 0.7 + countScore * 0.3) * 100;
	}

	/**
	 * Gera sugestões de gêneros baseado no título
	 */
	static suggestGenres(title: string): string[] {
		const suggestions: string[] = [];
		const normalizedTitle = this.normalizeString(title);

		const genreKeywords = {
			Ação: ["action", "battle", "fight", "war", "ninja", "sword", "martial"],
			Romance: ["love", "romance", "romantic", "heart", "dating", "kiss"],
			Comédia: ["comedy", "funny", "humor", "laugh", "comic", "gag"],
			Drama: ["drama", "serious", "emotional", "tear", "sad", "tragic"],
			Fantasia: ["magic", "fantasy", "wizard", "dragon", "elf", "fairy"],
			"Ficção Científica": ["sci-fi", "science", "space", "robot", "future", "alien"],
			Horror: ["horror", "scary", "ghost", "zombie", "monster", "fear"],
			Mistério: ["mystery", "detective", "investigation", "crime", "puzzle"],
			"Slice of Life": ["school", "daily", "life", "student", "everyday", "normal"],
			Esportes: ["sport", "soccer", "baseball", "basketball", "volleyball", "tennis"],
		};

		for (const [genre, keywords] of Object.entries(genreKeywords)) {
			for (const keyword of keywords) {
				if (normalizedTitle.includes(keyword)) {
					suggestions.push(genre);
					break;
				}
			}
		}

		return [...new Set(suggestions)];
	}

	/**
	 * Valida se os dados da obra estão consistentes
	 */
	static validateWorkData(work: Partial<Work>): string[] {
		const errors: string[] = [];

		if (work.publicationYear && work.publicationYear > new Date().getFullYear()) {
			errors.push("Ano de publicação não pode ser no futuro");
		}

		if (work.totalChapters && work.totalChapters < 0) {
			errors.push("Número de capítulos não pode ser negativo");
		}

		if (work.averageRating && (work.averageRating < 0 || work.averageRating > 10)) {
			errors.push("Nota média deve estar entre 0 e 10");
		}

		if (work.totalRatings && work.totalRatings < 0) {
			errors.push("Total de avaliações não pode ser negativo");
		}

		if (work.genres && work.genres.length > 10) {
			errors.push("Máximo de 10 gêneros permitidos");
		}

		return errors;
	}

	/**
	 * Gera slug baseado no título da obra
	 */
	static generateSlug(title: string): string {
		return this.normalizeString(title)
			.replace(/\s+/g, "-")
			.replace(/[^a-z0-9-]/g, "")
			.replace(/-+/g, "-")
			.replace(/^-|-$/g, "");
	}

	/**
	 * Formata estatísticas para exibição
	 */
	static formatWorkStats(stats: any): any {
		return {
			...stats,
			totalWorks: stats.totalWorks.toLocaleString(),
			worksByType: Object.entries(stats.worksByType).map(([type, count]) => ({
				type: this.formatWorkType(type as WorkType),
				count: (count as number).toLocaleString(),
			})),
			worksByStatus: Object.entries(stats.worksByStatus).map(([status, count]) => ({
				status: this.formatWorkStatus(status as WorkStatus),
				count: (count as number).toLocaleString(),
			})),
		};
	}

	/**
	 * Formata tipo de obra para exibição
	 */
	static formatWorkType(type: WorkType): string {
		const typeMap = {
			[WorkType.MANGA]: "Mangá",
			[WorkType.MANHWA]: "Manhwa",
			[WorkType.MANHUA]: "Manhua",
		};
		return typeMap[type] || type;
	}

	/**
	 * Formata status da obra para exibição
	 */
	static formatWorkStatus(status: WorkStatus): string {
		const statusMap = {
			[WorkStatus.ONGOING]: "Em Andamento",
			[WorkStatus.COMPLETED]: "Completo",
			[WorkStatus.HIATUS]: "Em Hiato",
			[WorkStatus.CANCELLED]: "Cancelado",
		};
		return statusMap[status] || status;
	}

	/**
	 * Extrai ano de uma string de data
	 */
	static extractYear(dateString: string): number | null {
		const match = dateString.match(/\b(19|20)\d{2}\b/);
		return match ? parseInt(match[0]) : null;
	}

	/**
	 * Calcula a idade da obra em anos
	 */
	static calculateWorkAge(publicationYear: number): number {
		return new Date().getFullYear() - publicationYear;
	}

	/**
	 * Determina se uma obra é considerada "clássica"
	 */
	static isClassic(work: Work): boolean {
		if (!work.publicationYear) return false;

		const age = this.calculateWorkAge(work.publicationYear);
		const highRating = work.averageRating >= 8.0;
		const manyRatings = work.totalRatings >= 500;

		return age >= 10 && highRating && manyRatings;
	}

	/**
	 * Gera hash simples para cache
	 */
	static generateCacheKey(prefix: string, params: any): string {
		const hash = JSON.stringify(params)
			.split("")
			.reduce((a, b) => {
				a = (a << 5) - a + b.charCodeAt(0);
				return a & a;
			}, 0);

		return `${prefix}:${Math.abs(hash)}`;
	}
}

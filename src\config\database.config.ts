import { registerAs } from "@nestjs/config";
import { TypeOrmModuleOptions } from "@nestjs/typeorm";

export default registerAs(
	"database",
	(): TypeOrmModuleOptions => ({
		type: "postgres",
		host: process.env.DATABASE_HOST || "localhost",
		port: parseInt(process.env.DATABASE_PORT, 10) || 5432,
		username: process.env.DATABASE_USERNAME || "manga_user",
		password: process.env.DATABASE_PASSWORD || "manga_password",
		database: process.env.DATABASE_NAME || "manga_tracker",
		entities: [__dirname + "/../**/*.entity{.ts,.js}"],
		synchronize: process.env.NODE_ENV === "development",
		logging: process.env.NODE_ENV === "development",
		ssl: process.env.NODE_ENV === "production" ? { rejectUnauthorized: false } : false,
	})
);

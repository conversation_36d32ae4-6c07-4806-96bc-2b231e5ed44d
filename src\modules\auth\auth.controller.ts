import { Controller, Post, Body, HttpCode, HttpStatus, Get, UseGuards, Request } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiBody } from "@nestjs/swagger";
import { AuthService } from "./auth.service";
import { RegisterDto } from "./dto/register.dto";
import { LoginDto } from "./dto/login.dto";
import { JwtAuthGuard } from "./guards/jwt-auth.guard";

@ApiTags("Autenticação")
@Controller("auth")
export class AuthController {
	constructor(private readonly authService: AuthService) {}

	@Post("register")
	@ApiOperation({
		summary: "Registrar novo usuário",
		description: "Cria uma nova conta de usuário no sistema",
	})
	@ApiBody({ type: RegisterDto })
	@ApiResponse({
		status: 201,
		description: "Usuário criado com sucesso",
		schema: {
			type: "object",
			properties: {
				user: {
					type: "object",
					properties: {
						id: { type: "number", example: 1 },
						username: { type: "string", example: "otaku_reader" },
						email: { type: "string", example: "<EMAIL>" },
						fullName: { type: "string", example: "João Silva" },
						avatar: { type: "string", example: "https://example.com/avatar.jpg" },
						bio: { type: "string", example: "Leitor apaixonado por mangás" },
						isPublic: { type: "boolean", example: true },
						createdAt: { type: "string", format: "date-time" },
						updatedAt: { type: "string", format: "date-time" },
					},
				},
				token: { type: "string", example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." },
			},
		},
	})
	@ApiResponse({
		status: 409,
		description: "Nome de usuário ou email já está em uso",
	})
	@ApiResponse({
		status: 400,
		description: "Dados inválidos",
	})
	async register(@Body() registerDto: RegisterDto) {
		return this.authService.register(registerDto);
	}

	@Post("login")
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: "Fazer login",
		description: "Autentica um usuário e retorna um token JWT",
	})
	@ApiBody({ type: LoginDto })
	@ApiResponse({
		status: 200,
		description: "Login realizado com sucesso",
		schema: {
			type: "object",
			properties: {
				user: {
					type: "object",
					properties: {
						id: { type: "number", example: 1 },
						username: { type: "string", example: "otaku_reader" },
						email: { type: "string", example: "<EMAIL>" },
						fullName: { type: "string", example: "João Silva" },
						avatar: { type: "string", example: "https://example.com/avatar.jpg" },
						bio: { type: "string", example: "Leitor apaixonado por mangás" },
						isPublic: { type: "boolean", example: true },
						createdAt: { type: "string", format: "date-time" },
						updatedAt: { type: "string", format: "date-time" },
					},
				},
				token: { type: "string", example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." },
			},
		},
	})
	@ApiResponse({
		status: 401,
		description: "Credenciais inválidas",
	})
	async login(@Body() loginDto: LoginDto) {
		return this.authService.login(loginDto);
	}

	@Get("profile")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth()
	@ApiOperation({
		summary: "Obter perfil do usuário",
		description: "Retorna os dados do usuário autenticado",
	})
	@ApiResponse({
		status: 200,
		description: "Perfil do usuário",
		schema: {
			type: "object",
			properties: {
				id: { type: "number", example: 1 },
				username: { type: "string", example: "otaku_reader" },
				email: { type: "string", example: "<EMAIL>" },
				fullName: { type: "string", example: "João Silva" },
				avatar: { type: "string", example: "https://example.com/avatar.jpg" },
				bio: { type: "string", example: "Leitor apaixonado por mangás" },
				isPublic: { type: "boolean", example: true },
				createdAt: { type: "string", format: "date-time" },
				updatedAt: { type: "string", format: "date-time" },
			},
		},
	})
	@ApiResponse({
		status: 401,
		description: "Token inválido ou expirado",
	})
	async getProfile(@Request() req) {
		const { password, ...userWithoutPassword } = req.user;
		return userWithoutPassword;
	}
}

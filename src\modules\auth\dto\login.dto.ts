import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString } from "class-validator";

export class LoginDto {
	@ApiProperty({
		description: "Nome de usuário ou email",
		example: "otaku_reader",
	})
	@IsNotEmpty()
	@IsString()
	usernameOrEmail: string;

	@ApiProperty({
		description: "Senha do usuário",
		example: "MinhaSenh@123",
	})
	@IsNotEmpty()
	@IsString()
	password: string;
}

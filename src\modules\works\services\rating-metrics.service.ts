import { Injectable, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Work, RatingMetrics } from "../../../entities";
import { RatingCalculatorService, IRatingCalculationResult } from "./rating-calculator.service";

@Injectable()
export class RatingMetricsService {
	private readonly logger = new Logger(RatingMetricsService.name);

	constructor(
		@InjectRepository(Work)
		private readonly workRepository: Repository<Work>,
		@InjectRepository(RatingMetrics)
		private readonly ratingMetricsRepository: Repository<RatingMetrics>,
		private readonly ratingCalculatorService: RatingCalculatorService
	) {}

	/**
	 * Atualiza todas as métricas de rating para uma obra
	 */
	async updateWorkRatingMetrics(workId: number): Promise<void> {
		try {
			this.logger.log(`Atualizando métricas de rating para obra ${workId}`);

			const calculationResult = await this.ratingCalculatorService.calculateAdvancedRating(workId);

			// Salva métricas detalhadas
			await this.saveRatingMetrics(workId, calculationResult);

			// Atualiza campos principais na entidade Work
			await this.updateWorkMainFields(workId, calculationResult);

			this.logger.log(`Métricas atualizadas com sucesso para obra ${workId}`);
		} catch (error) {
			this.logger.error(`Erro ao atualizar métricas para obra ${workId}:`, error);
			throw error;
		}
	}

	/**
	 * Salva métricas detalhadas na tabela rating_metrics
	 */
	private async saveRatingMetrics(workId: number, result: IRatingCalculationResult): Promise<void> {
		const ratingMetrics = this.ratingMetricsRepository.create({
			workId,
			simpleAverage: result.simpleAverage,
			bayesianAverage: result.bayesianAverage,
			median: result.median,
			standardDeviation: result.standardDeviation,
			distribution: result.distribution,
			confidenceScore: result.confidenceScore,
			outliersCount: result.outliersCount,
			trendScore: result.trendScore,
			ratingVelocity: result.ratingVelocity,
			qualityScore: result.qualityScore,
			totalRatings: result.totalRatings,
			recentRatings7d: result.recentRatings7d,
			recentRatings30d: result.recentRatings30d,
			calculatedAt: new Date(),
		});

		await this.ratingMetricsRepository.save(ratingMetrics);
	}

	/**
	 * Atualiza campos principais na entidade Work
	 */
	private async updateWorkMainFields(workId: number, result: IRatingCalculationResult): Promise<void> {
		await this.workRepository.update(workId, {
			averageRating: result.simpleAverage,
			totalRatings: result.totalRatings,
			weightedRating: result.bayesianAverage,
			trendingScore: this.calculateTrendingScore(result),
			ratingDistribution: result.distribution,
			recentTrend: result.trendScore,
			qualityScore: result.qualityScore,
			recentRatingsCount: result.recentRatings30d,
		});
	}

	/**
	 * Calcula score de trending combinando múltiplos fatores
	 */
	private calculateTrendingScore(result: IRatingCalculationResult): number {
		const recentActivityWeight = 0.4;
		const trendWeight = 0.3;
		const qualityWeight = 0.3;

		// Score baseado em atividade recente (0-100)
		const activityScore = Math.min((result.recentRatings7d / 10) * 100, 100);

		// Score baseado na tendência (-100 a 100, normalizado para 0-100)
		const trendScore = ((result.trendScore + 1) / 2) * 100;

		// Score baseado na qualidade (0-100)
		const qualityScore = result.qualityScore;

		return activityScore * recentActivityWeight + trendScore * trendWeight + qualityScore * qualityWeight;
	}

	/**
	 * Obtém métricas mais recentes de uma obra
	 */
	async getLatestMetrics(workId: number): Promise<RatingMetrics | null> {
		return this.ratingMetricsRepository.findOne({
			where: { workId },
			order: { calculatedAt: "DESC" },
		});
	}

	/**
	 * Obtém histórico de métricas de uma obra
	 */
	async getMetricsHistory(workId: number, limit: number = 30): Promise<RatingMetrics[]> {
		return this.ratingMetricsRepository.find({
			where: { workId },
			order: { calculatedAt: "DESC" },
			take: limit,
		});
	}

	/**
	 * Atualiza métricas para todas as obras (job em lote)
	 */
	async updateAllWorksMetrics(batchSize: number = 100): Promise<void> {
		this.logger.log("Iniciando atualização em lote de métricas de rating");

		let offset = 0;
		let hasMore = true;

		while (hasMore) {
			const works = await this.workRepository.find({
				select: ["id"],
				skip: offset,
				take: batchSize,
			});

			if (works.length === 0) {
				hasMore = false;
				break;
			}

			this.logger.log(`Processando lote ${offset / batchSize + 1}: ${works.length} obras`);

			// Processa em paralelo com limite
			const promises = works.map(work =>
				this.updateWorkRatingMetrics(work.id).catch(error => {
					this.logger.error(`Erro ao processar obra ${work.id}:`, error);
				})
			);

			await Promise.all(promises);

			offset += batchSize;
		}

		this.logger.log("Atualização em lote concluída");
	}

	/**
	 * Limpa métricas antigas (mantém apenas as últimas N entradas por obra)
	 */
	async cleanupOldMetrics(keepLast: number = 100): Promise<void> {
		this.logger.log(`Limpando métricas antigas, mantendo últimas ${keepLast} por obra`);

		const works = await this.workRepository.find({ select: ["id"] });

		for (const work of works) {
			const metricsToDelete = await this.ratingMetricsRepository
				.createQueryBuilder("metrics")
				.where("metrics.workId = :workId", { workId: work.id })
				.orderBy("metrics.calculatedAt", "DESC")
				.skip(keepLast)
				.getMany();

			if (metricsToDelete.length > 0) {
				await this.ratingMetricsRepository.remove(metricsToDelete);
				this.logger.log(`Removidas ${metricsToDelete.length} métricas antigas da obra ${work.id}`);
			}
		}

		this.logger.log("Limpeza de métricas antigas concluída");
	}

	/**
	 * Obtém estatísticas gerais do sistema de rating
	 */
	async getSystemRatingStats(): Promise<{
		totalWorks: number;
		totalRatings: number;
		averageRating: number;
		averageConfidence: number;
		topQualityWorks: Array<{ workId: number; title: string; qualityScore: number }>;
		trendingWorks: Array<{ workId: number; title: string; trendingScore: number }>;
		systemHealth: string;
	}> {
		const [totalWorks, totalRatings, avgRating, avgConfidence] = await Promise.all([
			this.workRepository.count(),
			this.workRepository
				.createQueryBuilder("work")
				.select("SUM(work.totalRatings)", "total")
				.getRawOne()
				.then(result => parseInt(result.total) || 0),
			this.workRepository
				.createQueryBuilder("work")
				.select("AVG(work.averageRating)", "avg")
				.getRawOne()
				.then(result => parseFloat(result.avg) || 0),
			this.ratingMetricsRepository
				.createQueryBuilder("metrics")
				.select("AVG(metrics.confidenceScore)", "avg")
				.getRawOne()
				.then(result => parseFloat(result.avg) || 0),
		]);

		const topQualityWorks = await this.workRepository
			.createQueryBuilder("work")
			.select(["work.id", "work.title", "work.qualityScore"])
			.orderBy("work.qualityScore", "DESC")
			.limit(10)
			.getMany()
			.then(works => works.map(w => ({ workId: w.id, title: w.title, qualityScore: w.qualityScore })));

		const trendingWorks = await this.workRepository
			.createQueryBuilder("work")
			.select(["work.id", "work.title", "work.trendingScore"])
			.orderBy("work.trendingScore", "DESC")
			.limit(10)
			.getMany()
			.then(works => works.map(w => ({ workId: w.id, title: w.title, trendingScore: w.trendingScore })));

		// Calcular saúde do sistema
		const systemHealth = this.calculateSystemHealth({
			averageRating: avgRating,
			averageConfidence: avgConfidence,
			totalRatings,
		});

		return {
			totalWorks,
			totalRatings,
			averageRating: avgRating,
			averageConfidence: avgConfidence,
			topQualityWorks,
			trendingWorks,
			systemHealth,
		};
	}

	/**
	 * Calcula a saúde geral do sistema baseado nas métricas
	 */
	private calculateSystemHealth(stats: { averageRating: number; averageConfidence: number; totalRatings: number }): string {
		const { averageRating, averageConfidence, totalRatings } = stats;

		let healthScore = 0;

		// Score baseado na média geral (0-40 pontos)
		if (averageRating >= 7.5) healthScore += 40;
		else if (averageRating >= 6.5) healthScore += 30;
		else if (averageRating >= 5.5) healthScore += 20;
		else healthScore += 10;

		// Score baseado na confiança (0-30 pontos)
		if (averageConfidence >= 80) healthScore += 30;
		else if (averageConfidence >= 60) healthScore += 20;
		else if (averageConfidence >= 40) healthScore += 10;

		// Score baseado no volume de avaliações (0-30 pontos)
		if (totalRatings >= 10000) healthScore += 30;
		else if (totalRatings >= 5000) healthScore += 20;
		else if (totalRatings >= 1000) healthScore += 10;

		if (healthScore >= 90) return "Excelente";
		if (healthScore >= 70) return "Bom";
		if (healthScore >= 50) return "Regular";
		return "Precisa Atenção";
	}
}

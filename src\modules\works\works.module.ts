import { <PERSON><PERSON><PERSON>, MiddlewareConsumer, RequestMethod } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { WorksService } from "./works.service";
import { WorksController } from "./works.controller";
import { Work, UserWork, RatingMetrics } from "../../entities";
import { SimpleCacheMiddleware } from "./middleware/cache.middleware";
import { RatingCalculatorService } from "./services/rating-calculator.service";
import { RatingMetricsService } from "./services/rating-metrics.service";
import { RatingMetricsTask } from "./tasks/rating-metrics.task";

@Module({
	imports: [TypeOrmModule.forFeature([Work, UserWork, RatingMetrics])],
	controllers: [WorksController],
	providers: [WorksService, RatingCalculatorService, RatingMetricsService, RatingMetricsTask, SimpleCacheMiddleware],
	exports: [WorksService, RatingMetricsService],
})
export class WorksModule {
	configure(consumer: MiddlewareConsumer) {
		consumer
			.apply(SimpleCacheMiddleware)
			.forRoutes(
				{ path: "works/popular", method: RequestMethod.GET },
				{ path: "works/random", method: RequestMethod.GET },
				{ path: "works/statistics", method: RequestMethod.GET },
				{ path: "works/genre/*", method: RequestMethod.GET }
			);
	}
}

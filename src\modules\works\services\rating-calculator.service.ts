import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Work, UserWork, RatingMetrics } from "../../../entities";

export interface IRatingCalculationResult {
	simpleAverage: number;
	bayesianAverage: number;
	median: number;
	standardDeviation: number;
	distribution: number[];
	confidenceScore: number;
	outliersCount: number;
	trendScore: number;
	ratingVelocity: number;
	qualityScore: number;
	totalRatings: number;
	recentRatings7d: number;
	recentRatings30d: number;
}

@Injectable()
export class RatingCalculatorService {
	constructor(
		@InjectRepository(UserWork)
		private readonly userWorkRepository: Repository<UserWork>,
		@InjectRepository(RatingMetrics)
		private readonly ratingMetricsRepository: Repository<RatingMetrics>
	) {}

	/**
	 * Calcula todas as métricas de rating para uma obra
	 */
	async calculateAdvancedRating(workId: number): Promise<IRatingCalculationResult> {
		const ratings = await this.getRatingsForWork(workId);

		if (ratings.length === 0) {
			return this.getEmptyResult();
		}

		const simpleAverage = this.calculateSimpleAverage(ratings);
		const bayesianAverage = this.calculateBayesianAverage(ratings, simpleAverage);
		const median = this.calculateMedian(ratings);
		const standardDeviation = this.calculateStandardDeviation(ratings, simpleAverage);
		const distribution = this.calculateDistribution(ratings);
		const confidenceScore = this.calculateConfidenceScore(ratings, standardDeviation);
		const outliersCount = this.detectOutliers(ratings, simpleAverage, standardDeviation).length;
		const trendScore = await this.calculateTrendScore(workId);
		const ratingVelocity = await this.calculateRatingVelocity(workId);
		const qualityScore = this.calculateQualityScore({
			simpleAverage,
			bayesianAverage,
			confidenceScore,
			totalRatings: ratings.length,
			standardDeviation,
		});

		const recentRatings = await this.getRecentRatingsCount(workId);

		return {
			simpleAverage,
			bayesianAverage,
			median,
			standardDeviation,
			distribution,
			confidenceScore,
			outliersCount,
			trendScore,
			ratingVelocity,
			qualityScore,
			totalRatings: ratings.length,
			recentRatings7d: recentRatings.last7Days,
			recentRatings30d: recentRatings.last30Days,
		};
	}

	/**
	 * Calcula média simples
	 */
	private calculateSimpleAverage(ratings: number[]): number {
		if (ratings.length === 0) return 0;
		return ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;
	}

	/**
	 * Calcula Bayesian Average (média ponderada)
	 * Fórmula: (C * m + Σ(ratings)) / (C + n)
	 * Onde C = confiança mínima, m = média global, n = número de ratings
	 */
	private calculateBayesianAverage(ratings: number[], simpleAverage: number): number {
		const C = 50; // Número mínimo de avaliações para confiança total
		const m = 7.0; // Média global assumida (pode ser calculada dinamicamente)
		const n = ratings.length;

		const sumRatings = ratings.reduce((sum, rating) => sum + rating, 0);
		return (C * m + sumRatings) / (C + n);
	}

	/**
	 * Calcula mediana
	 */
	private calculateMedian(ratings: number[]): number {
		const sorted = [...ratings].sort((a, b) => a - b);
		const mid = Math.floor(sorted.length / 2);

		if (sorted.length % 2 === 0) {
			return (sorted[mid - 1] + sorted[mid]) / 2;
		}
		return sorted[mid];
	}

	/**
	 * Calcula desvio padrão
	 */
	private calculateStandardDeviation(ratings: number[], average: number): number {
		if (ratings.length <= 1) return 0;

		const variance =
			ratings.reduce((sum, rating) => {
				return sum + Math.pow(rating - average, 2);
			}, 0) /
			(ratings.length - 1);

		return Math.sqrt(variance);
	}

	/**
	 * Calcula distribuição de notas (1-10)
	 */
	private calculateDistribution(ratings: number[]): number[] {
		const distribution = new Array(10).fill(0);

		ratings.forEach(rating => {
			const index = Math.min(Math.max(Math.floor(rating) - 1, 0), 9);
			distribution[index]++;
		});

		return distribution;
	}

	/**
	 * Calcula score de confiabilidade baseado no número de ratings e consistência
	 */
	private calculateConfidenceScore(ratings: number[], standardDeviation: number): number {
		const countScore = Math.min(ratings.length / 100, 1) * 50; // Máximo 50 pontos por quantidade
		const consistencyScore = Math.max(0, (2 - standardDeviation) / 2) * 50; // Máximo 50 pontos por consistência

		return Math.min(countScore + consistencyScore, 100);
	}

	/**
	 * Detecta outliers usando método IQR
	 */
	private detectOutliers(ratings: number[], average: number, standardDeviation: number): number[] {
		const threshold = 2 * standardDeviation;
		return ratings.filter(rating => Math.abs(rating - average) > threshold);
	}

	/**
	 * Calcula score de tendência baseado em avaliações recentes
	 */
	private async calculateTrendScore(workId: number): Promise<number> {
		const thirtyDaysAgo = new Date();
		thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

		const recentRatings = await this.userWorkRepository
			.createQueryBuilder("userWork")
			.select("userWork.rating", "rating")
			.addSelect("userWork.updatedAt", "updatedAt")
			.where("userWork.workId = :workId", { workId })
			.andWhere("userWork.rating IS NOT NULL")
			.andWhere("userWork.updatedAt >= :thirtyDaysAgo", { thirtyDaysAgo })
			.orderBy("userWork.updatedAt", "ASC")
			.getRawMany();

		if (recentRatings.length < 5) return 0;

		// Calcula tendência usando regressão linear simples
		const n = recentRatings.length;
		let sumX = 0,
			sumY = 0,
			sumXY = 0,
			sumX2 = 0;

		recentRatings.forEach((rating, index) => {
			const x = index;
			const y = parseFloat(rating.rating);
			sumX += x;
			sumY += y;
			sumXY += x * y;
			sumX2 += x * x;
		});

		const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
		return Math.max(-1, Math.min(1, slope)); // Normaliza entre -1 e 1
	}

	/**
	 * Calcula velocidade de mudança do rating
	 */
	private async calculateRatingVelocity(workId: number): Promise<number> {
		const metrics = await this.ratingMetricsRepository
			.createQueryBuilder("metrics")
			.where("metrics.workId = :workId", { workId })
			.orderBy("metrics.calculatedAt", "DESC")
			.limit(2)
			.getMany();

		if (metrics.length < 2) return 0;

		const [current, previous] = metrics;
		const timeDiff = (current.calculatedAt.getTime() - previous.calculatedAt.getTime()) / (1000 * 60 * 60 * 24); // dias
		const ratingDiff = current.simpleAverage - previous.simpleAverage;

		return timeDiff > 0 ? ratingDiff / timeDiff : 0;
	}

	/**
	 * Calcula score de qualidade geral
	 */
	private calculateQualityScore(params: {
		simpleAverage: number;
		bayesianAverage: number;
		confidenceScore: number;
		totalRatings: number;
		standardDeviation: number;
	}): number {
		const { simpleAverage, bayesianAverage, confidenceScore, totalRatings, standardDeviation } = params;

		// Peso dos fatores
		const ratingWeight = 0.4;
		const confidenceWeight = 0.3;
		const popularityWeight = 0.2;
		const consistencyWeight = 0.1;

		const ratingScore = (bayesianAverage / 10) * 100;
		const popularityScore = Math.min(Math.log(totalRatings + 1) / Math.log(1000), 1) * 100;
		const consistencyScore = Math.max(0, (3 - standardDeviation) / 3) * 100;

		return ratingScore * ratingWeight + confidenceScore * confidenceWeight + popularityScore * popularityWeight + consistencyScore * consistencyWeight;
	}

	/**
	 * Obtém contagem de avaliações recentes
	 */
	private async getRecentRatingsCount(workId: number): Promise<{ last7Days: number; last30Days: number }> {
		const sevenDaysAgo = new Date();
		sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

		const thirtyDaysAgo = new Date();
		thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

		const [last7Days, last30Days] = await Promise.all([
			this.userWorkRepository
				.createQueryBuilder("userWork")
				.where("userWork.workId = :workId", { workId })
				.andWhere("userWork.updatedAt >= :sevenDaysAgo", { sevenDaysAgo })
				.andWhere("userWork.rating IS NOT NULL")
				.getCount(),
			this.userWorkRepository
				.createQueryBuilder("userWork")
				.where("userWork.workId = :workId", { workId })
				.andWhere("userWork.updatedAt >= :thirtyDaysAgo", { thirtyDaysAgo })
				.andWhere("userWork.rating IS NOT NULL")
				.getCount(),
		]);

		return { last7Days, last30Days };
	}

	/**
	 * Obtém todas as avaliações de uma obra
	 */
	private async getRatingsForWork(workId: number): Promise<number[]> {
		const userWorks = await this.userWorkRepository
			.createQueryBuilder("userWork")
			.select("userWork.rating", "rating")
			.where("userWork.workId = :workId", { workId })
			.andWhere("userWork.rating IS NOT NULL")
			.getRawMany();

		return userWorks.map(uw => parseFloat(uw.rating)).filter(rating => !isNaN(rating));
	}

	/**
	 * Retorna resultado vazio para obras sem avaliações
	 */
	private getEmptyResult(): IRatingCalculationResult {
		return {
			simpleAverage: 0,
			bayesianAverage: 0,
			median: 0,
			standardDeviation: 0,
			distribution: new Array(10).fill(0),
			confidenceScore: 0,
			outliersCount: 0,
			trendScore: 0,
			ratingVelocity: 0,
			qualityScore: 0,
			totalRatings: 0,
			recentRatings7d: 0,
			recentRatings30d: 0,
		};
	}
}

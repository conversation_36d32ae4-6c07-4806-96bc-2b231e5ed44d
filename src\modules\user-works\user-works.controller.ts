import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, Query, ParseIntPipe } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiParam } from "@nestjs/swagger";
import { UserWorksService } from "./user-works.service";
import { CreateUserWorkDto } from "./dto/create-user-work.dto";
import { UpdateUserWorkDto } from "./dto/update-user-work.dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { PaginationDto } from "../../common/dto/pagination.dto";
import { UserWork, ReadingStatus } from "../../entities";

@ApiTags("Biblioteca")
@Controller("user-works")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UserWorksController {
	constructor(private readonly userWorksService: UserWorksService) {}

	@Post()
	@ApiOperation({
		summary: "Adicionar obra à biblioteca",
		description: "Adiciona uma obra à biblioteca pessoal do usuário",
	})
	@ApiResponse({
		status: 201,
		description: "Obra adicionada à biblioteca com sucesso",
		type: UserWork,
	})
	@ApiResponse({
		status: 409,
		description: "Esta obra já está na sua biblioteca",
	})
	@ApiResponse({
		status: 404,
		description: "Obra não encontrada",
	})
	create(@Request() req, @Body() createUserWorkDto: CreateUserWorkDto) {
		return this.userWorksService.create(req.user.id, createUserWorkDto);
	}

	@Get()
	@ApiOperation({
		summary: "Listar biblioteca do usuário",
		description: "Retorna a biblioteca pessoal do usuário com filtros opcionais",
	})
	@ApiQuery({ name: "page", required: false, type: Number, description: "Número da página" })
	@ApiQuery({ name: "limit", required: false, type: Number, description: "Itens por página" })
	@ApiQuery({ name: "status", required: false, enum: ReadingStatus, description: "Filtrar por status" })
	@ApiQuery({ name: "isFavorite", required: false, type: Boolean, description: "Filtrar favoritos" })
	@ApiQuery({ name: "search", required: false, type: String, description: "Buscar por título" })
	@ApiQuery({ name: "tag", required: false, type: String, description: "Filtrar por tag" })
	@ApiResponse({
		status: 200,
		description: "Biblioteca retornada com sucesso",
		schema: {
			type: "object",
			properties: {
				data: {
					type: "array",
					items: { $ref: "#/components/schemas/UserWork" },
				},
				meta: {
					type: "object",
					properties: {
						page: { type: "number" },
						limit: { type: "number" },
						total: { type: "number" },
						totalPages: { type: "number" },
						hasNext: { type: "boolean" },
						hasPrev: { type: "boolean" },
					},
				},
			},
		},
	})
	findAll(
		@Request() req,
		@Query() paginationDto: PaginationDto,
		@Query("status") status?: ReadingStatus,
		@Query("isFavorite") isFavorite?: boolean,
		@Query("search") search?: string,
		@Query("tag") tag?: string
	) {
		const filters = { status, isFavorite, search, tag };
		return this.userWorksService.findAllByUser(req.user.id, paginationDto, filters);
	}

	@Get("statistics")
	@ApiOperation({
		summary: "Estatísticas da biblioteca",
		description: "Retorna estatísticas da biblioteca do usuário",
	})
	@ApiResponse({
		status: 200,
		description: "Estatísticas retornadas com sucesso",
		schema: {
			type: "object",
			properties: {
				total: { type: "number", description: "Total de obras" },
				reading: { type: "number", description: "Obras sendo lidas" },
				completed: { type: "number", description: "Obras completadas" },
				onHold: { type: "number", description: "Obras pausadas" },
				dropped: { type: "number", description: "Obras abandonadas" },
				planToRead: { type: "number", description: "Obras para ler" },
				favorites: { type: "number", description: "Obras favoritas" },
				averageRating: { type: "number", description: "Nota média" },
			},
		},
	})
	getStatistics(@Request() req) {
		return this.userWorksService.getStatistics(req.user.id);
	}

	@Get(":id")
	@ApiOperation({
		summary: "Obter obra da biblioteca",
		description: "Retorna uma obra específica da biblioteca do usuário",
	})
	@ApiParam({ name: "id", type: Number, description: "ID da obra na biblioteca" })
	@ApiResponse({
		status: 200,
		description: "Obra encontrada com sucesso",
		type: UserWork,
	})
	@ApiResponse({
		status: 404,
		description: "Obra não encontrada na sua biblioteca",
	})
	findOne(@Param("id", ParseIntPipe) id: number, @Request() req) {
		return this.userWorksService.findOne(id, req.user.id);
	}

	@Patch(":id")
	@ApiOperation({
		summary: "Atualizar obra da biblioteca",
		description: "Atualiza informações de uma obra na biblioteca do usuário",
	})
	@ApiParam({ name: "id", type: Number, description: "ID da obra na biblioteca" })
	@ApiResponse({
		status: 200,
		description: "Obra atualizada com sucesso",
		type: UserWork,
	})
	@ApiResponse({
		status: 404,
		description: "Obra não encontrada na sua biblioteca",
	})
	update(@Param("id", ParseIntPipe) id: number, @Body() updateUserWorkDto: UpdateUserWorkDto, @Request() req) {
		return this.userWorksService.update(id, req.user.id, updateUserWorkDto);
	}

	@Patch(":id/progress/:chapter")
	@ApiOperation({
		summary: "Atualizar progresso de leitura",
		description: "Atualiza o capítulo atual de uma obra",
	})
	@ApiParam({ name: "id", type: Number, description: "ID da obra na biblioteca" })
	@ApiParam({ name: "chapter", type: Number, description: "Número do capítulo atual" })
	@ApiResponse({
		status: 200,
		description: "Progresso atualizado com sucesso",
		type: UserWork,
	})
	@ApiResponse({
		status: 404,
		description: "Obra não encontrada na sua biblioteca",
	})
	updateProgress(@Param("id", ParseIntPipe) id: number, @Param("chapter", ParseIntPipe) chapter: number, @Request() req) {
		return this.userWorksService.updateProgress(id, req.user.id, chapter);
	}

	@Delete(":id")
	@ApiOperation({
		summary: "Remover obra da biblioteca",
		description: "Remove uma obra da biblioteca do usuário",
	})
	@ApiParam({ name: "id", type: Number, description: "ID da obra na biblioteca" })
	@ApiResponse({
		status: 200,
		description: "Obra removida da biblioteca com sucesso",
	})
	@ApiResponse({
		status: 404,
		description: "Obra não encontrada na sua biblioteca",
	})
	remove(@Param("id", ParseIntPipe) id: number, @Request() req) {
		return this.userWorksService.remove(id, req.user.id);
	}
}

import { MigrationInterface, QueryRunner } from "typeorm";

export class AddAdvancedRatingMetrics1734567890123 implements MigrationInterface {
	name = "AddAdvancedRatingMetrics1734567890123";

	public async up(queryRunner: QueryRunner): Promise<void> {
		// Criar tabela rating_metrics
		await queryRunner.query(`
            CREATE TABLE "rating_metrics" (
                "id" SERIAL NOT NULL,
                "workId" integer NOT NULL,
                "simpleAverage" numeric(3,2) NOT NULL,
                "bayesianAverage" numeric(3,2) NOT NULL,
                "median" numeric(3,2) NOT NULL,
                "standardDeviation" numeric(4,3) NOT NULL,
                "distribution" text NOT NULL,
                "confidenceScore" numeric(5,2) NOT NULL,
                "outliersCount" integer NOT NULL DEFAULT '0',
                "trendScore" numeric(3,2) NOT NULL DEFAULT '0',
                "ratingVelocity" numeric(4,3) NOT NULL DEFAULT '0',
                "qualityScore" numeric(5,2) NOT NULL,
                "totalRatings" integer NOT NULL,
                "recentRatings7d" integer NOT NULL DEFAULT '0',
                "recentRatings30d" integer NOT NULL DEFAULT '0',
                "calculatedAt" TIMESTAMP NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_rating_metrics" PRIMARY KEY ("id")
            )
        `);

		// Adicionar índice na tabela rating_metrics
		await queryRunner.query(`
            CREATE INDEX "IDX_rating_metrics_workId_calculatedAt" 
            ON "rating_metrics" ("workId", "calculatedAt")
        `);

		// Adicionar novas colunas à tabela works
		await queryRunner.query(`
            ALTER TABLE "works" 
            ADD COLUMN "weightedRating" numeric(3,2) NOT NULL DEFAULT '0'
        `);

		await queryRunner.query(`
            ALTER TABLE "works" 
            ADD COLUMN "trendingScore" numeric(5,2) NOT NULL DEFAULT '0'
        `);

		await queryRunner.query(`
            ALTER TABLE "works" 
            ADD COLUMN "ratingDistribution" text
        `);

		await queryRunner.query(`
            ALTER TABLE "works" 
            ADD COLUMN "recentTrend" numeric(3,2) NOT NULL DEFAULT '0'
        `);

		await queryRunner.query(`
            ALTER TABLE "works" 
            ADD COLUMN "qualityScore" numeric(5,2) NOT NULL DEFAULT '0'
        `);

		await queryRunner.query(`
            ALTER TABLE "works" 
            ADD COLUMN "recentRatingsCount" integer NOT NULL DEFAULT '0'
        `);

		// Adicionar foreign key constraint
		await queryRunner.query(`
            ALTER TABLE "rating_metrics" 
            ADD CONSTRAINT "FK_rating_metrics_workId" 
            FOREIGN KEY ("workId") REFERENCES "works"("id") ON DELETE CASCADE
        `);

		// Inicializar valores padrão para obras existentes
		await queryRunner.query(`
            UPDATE "works" 
            SET 
                "weightedRating" = "averageRating",
                "qualityScore" = CASE 
                    WHEN "totalRatings" > 0 THEN 
                        LEAST(("averageRating" / 10.0 * 70) + (LOG(GREATEST("totalRatings", 1)) / LOG(100) * 30), 100)
                    ELSE 0 
                END
            WHERE "weightedRating" = 0
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Remover foreign key constraint
		await queryRunner.query(`
            ALTER TABLE "rating_metrics" 
            DROP CONSTRAINT "FK_rating_metrics_workId"
        `);

		// Remover colunas da tabela works
		await queryRunner.query(`ALTER TABLE "works" DROP COLUMN "recentRatingsCount"`);
		await queryRunner.query(`ALTER TABLE "works" DROP COLUMN "qualityScore"`);
		await queryRunner.query(`ALTER TABLE "works" DROP COLUMN "recentTrend"`);
		await queryRunner.query(`ALTER TABLE "works" DROP COLUMN "ratingDistribution"`);
		await queryRunner.query(`ALTER TABLE "works" DROP COLUMN "trendingScore"`);
		await queryRunner.query(`ALTER TABLE "works" DROP COLUMN "weightedRating"`);

		// Remover índice
		await queryRunner.query(`DROP INDEX "IDX_rating_metrics_workId_calculatedAt"`);

		// Remover tabela rating_metrics
		await queryRunner.query(`DROP TABLE "rating_metrics"`);
	}
}

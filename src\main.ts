import { NestFactory } from "@nestjs/core";
import { ValidationPipe } from "@nestjs/common";
import { SwaggerModule, DocumentBuilder } from "@nestjs/swagger";
import { AppModule } from "./app.module";

async function bootstrap() {
	const app = await NestFactory.create(AppModule);

	// Configurar CORS
	app.enableCors({
		origin: process.env.FRONTEND_URL || "http://localhost:3001",
		credentials: true,
	});

	// Configurar validação global
	app.useGlobalPipes(
		new ValidationPipe({
			whitelist: true,
			forbidNonWhitelisted: true,
			transform: true,
			transformOptions: {
				enableImplicitConversion: true,
			},
		})
	);

	// Configurar Swagger
	const config = new DocumentBuilder()
		.setTitle("Manga Tracker API")
		.setDescription(
			`
      API completa para gerenciamento de leitura de mangás, manhwas e manhuas.

      ## Funcionalidades principais:
      - 📚 **Gerenciamento de Obras**: Cadastro e busca de mangás, manhwas e manhuas
      - 👤 **Sistema de Usuários**: Registro, login e perfis personalizados
      - 📖 **Controle de Leitura**: Marcar capítulos, status de leitura e progresso
      - ⭐ **Avaliações**: Sistema de notas e comentários
      - 📝 **Anotações**: Criar notas pessoais sobre obras e capítulos
      - 🏆 **Rankings**: Criar listas personalizadas e rankings
      - 🔗 **Compartilhamento**: Compartilhar listas com outros usuários

      ## Tipos de obras suportados:
      - **Manga**: Quadrinhos japoneses
      - **Manhwa**: Quadrinhos coreanos
      - **Manhua**: Quadrinhos chineses

      ## Autenticação:
      A API utiliza JWT (JSON Web Tokens) para autenticação.
      Após fazer login, inclua o token no header Authorization: Bearer {token}
    `
		)
		.setVersion("1.0")
		.addBearerAuth(
			{
				type: "http",
				scheme: "bearer",
				bearerFormat: "JWT",
				name: "JWT",
				description: "Digite o token JWT",
				in: "header",
			},
			"JWT-auth"
		)
		.addTag("Autenticação", "Endpoints para registro, login e gerenciamento de usuários")
		.addTag("Obras", "Gerenciamento de mangás, manhwas e manhuas")
		.addTag("Biblioteca", "Gerenciamento da biblioteca pessoal do usuário")
		.addTag("Anotações", "Sistema de anotações e comentários")
		.addTag("Rankings", "Criação e gerenciamento de rankings pessoais")
		.addTag("Listas Compartilhadas", "Compartilhamento de listas entre usuários")
		.addServer("http://localhost:3000", "Servidor de desenvolvimento")
		.build();

	const document = SwaggerModule.createDocument(app, config);
	SwaggerModule.setup("api/docs", app, document, {
		swaggerOptions: {
			persistAuthorization: true,
			tagsSorter: "alpha",
			operationsSorter: "alpha",
		},
		customSiteTitle: "Manga Tracker API - Documentação",
		customfavIcon: "📚",
	});

	const port = process.env.PORT || 3000;
	await app.listen(port);

	console.log(`🚀 Aplicação rodando em: http://localhost:${port}`);
	console.log(`📚 Documentação Swagger: http://localhost:${port}/api/docs`);
}

bootstrap();

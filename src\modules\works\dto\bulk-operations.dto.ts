import { ApiProperty } from "@nestjs/swagger";
import { IsArray, IsNotEmpty, IsNumber, IsOptional } from "class-validator";
import { Type } from "class-transformer";
import { CreateWorkDto } from "./create-work.dto";
import { UpdateWorkDto } from "./update-work.dto";

export class BulkCreateWorksDto {
	@ApiProperty({
		description: "Lista de obras para criar",
		type: [CreateWorkDto],
	})
	@IsArray()
	@IsNotEmpty()
	@Type(() => CreateWorkDto)
	works: CreateWorkDto[];
}

export class BulkUpdateWorksDto {
	@ApiProperty({
		description: "Lista de atualizações de obras",
		type: [Object],
	})
	@IsArray()
	@IsNotEmpty()
	updates: Array<{
		id: number;
		data: UpdateWorkDto;
	}>;
}

export class BulkDeleteWorksDto {
	@ApiProperty({
		description: "IDs das obras para deletar",
		example: [1, 2, 3],
	})
	@IsArray()
	@IsNotEmpty()
	@IsNumber({}, { each: true })
	ids: number[];
}

export class WorkRecommendationDto {
	@ApiProperty({ description: "ID da obra base para recomendações" })
	@IsNotEmpty()
	@IsNumber()
	workId: number;

	@ApiProperty({ description: "Número de recomendações", required: false, default: 10 })
	@IsOptional()
	@IsNumber()
	@Type(() => Number)
	limit?: number;

	@ApiProperty({ description: "Incluir obras já lidas pelo usuário", required: false, default: false })
	@IsOptional()
	includeRead?: boolean;
}

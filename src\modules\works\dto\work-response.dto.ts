import { ApiProperty } from "@nestjs/swagger";
import { Work } from "../../../entities";

export class WorkResponseDto {
	@ApiProperty({ description: "Dados da obra", type: Work })
	data: Work;

	@ApiProperty({ description: "Timestamp da resposta" })
	timestamp: Date;

	@ApiProperty({ description: "Informações adicionais", required: false })
	meta?: {
		isInUserList?: boolean;
		userRating?: number;
		similarWorksCount?: number;
		recommendationsCount?: number;
	};

	constructor(work: Work, meta?: any) {
		this.data = work;
		this.timestamp = new Date();
		this.meta = meta;
	}
}

export class WorkStatsDto {
	@ApiProperty({ description: "Total de obras" })
	totalWorks: number;

	@ApiProperty({ description: "Obras por tipo" })
	worksByType: {
		manga: number;
		manhwa: number;
		manhua: number;
	};

	@ApiProperty({ description: "Obras por status" })
	worksByStatus: {
		ongoing: number;
		completed: number;
		hiatus: number;
		cancelled: number;
	};

	@ApiProperty({ description: "Gêneros mais populares" })
	topGenres: Array<{
		genre: string;
		count: number;
	}>;

	@ApiProperty({ description: "Autores mais prolíficos" })
	topAuthors: <AUTHORS>
		author: string;
		worksCount: number;
		averageRating: number;
	}>;

	@ApiProperty({ description: "Estatísticas de avaliação" })
	ratingStats: {
		averageRating: number;
		totalRatings: number;
		highlyRatedWorks: number; // Works with rating >= 8
	};
}

export class BulkOperationDto {
	@ApiProperty({ description: "IDs das obras afetadas" })
	affectedIds: number[];

	@ApiProperty({ description: "Número de obras processadas com sucesso" })
	successCount: number;

	@ApiProperty({ description: "Número de obras que falharam" })
	errorCount: number;

	@ApiProperty({ description: "Detalhes dos erros", required: false })
	errors?: Array<{
		id: number;
		error: string;
	}>;
}

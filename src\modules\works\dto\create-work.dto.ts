import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, IsUrl, <PERSON><PERSON><PERSON>th, <PERSON> } from "class-validator";
import { WorkType, WorkStatus } from "../../../entities";

export class CreateWorkDto {
	@ApiProperty({
		description: "Título da obra",
		example: "One Piece",
	})
	@IsNotEmpty()
	@IsString()
	title: string;

	@ApiPropertyOptional({
		description: "Título alternativo",
		example: "ワンピース",
	})
	@IsOptional()
	@IsString()
	alternativeTitle?: string;

	@ApiProperty({
		description: "Tipo da obra",
		enum: WorkType,
		example: WorkType.MANGA,
	})
	@IsNotEmpty()
	@IsEnum(WorkType)
	type: WorkType;

	@ApiProperty({
		description: "Autor(es) da obra",
		example: "<PERSON><PERSON><PERSON><PERSON>",
	})
	@IsNotEmpty()
	@IsString()
	author: string;

	@ApiPropertyOptional({
		description: "<PERSON>a(s) da obra",
		example: "<PERSON><PERSON><PERSON><PERSON>",
	})
	@IsOptional()
	@IsString()
	artist?: string;

	@ApiPropertyOptional({
		description: "Sinopse da obra",
		example: "A história de Monkey D. Luffy...",
	})
	@IsOptional()
	@IsString()
	@MaxLength(5000)
	synopsis?: string;

	@ApiPropertyOptional({
		description: "Gêneros da obra",
		example: ["Ação", "Aventura", "Comédia"],
		type: [String],
	})
	@IsOptional()
	@IsArray()
	@IsString({ each: true })
	genres?: string[];

	@ApiPropertyOptional({
		description: "Status da obra",
		enum: WorkStatus,
		example: WorkStatus.ONGOING,
	})
	@IsOptional()
	@IsEnum(WorkStatus)
	status?: WorkStatus;

	@ApiPropertyOptional({
		description: "Ano de publicação",
		example: 1997,
	})
	@IsOptional()
	@IsNumber()
	@Min(1900)
	publicationYear?: number;

	@ApiPropertyOptional({
		description: "Total de capítulos",
		example: 1000,
	})
	@IsOptional()
	@IsNumber()
	@Min(1)
	totalChapters?: number;

	@ApiPropertyOptional({
		description: "URL da capa",
		example: "https://example.com/cover.jpg",
	})
	@IsOptional()
	@IsUrl()
	coverImage?: string;
}

import { Injectable, NotFoundException, ConflictException, ForbiddenException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { UserWork, ReadingStatus, User, Work } from "../../entities";
import { CreateUserWorkDto } from "./dto/create-user-work.dto";
import { UpdateUserWorkDto } from "./dto/update-user-work.dto";
import { PaginationDto, PaginationResponseDto } from "../../common/dto/pagination.dto";
import { WorksService } from "../works/works.service";

@Injectable()
export class UserWorksService {
	constructor(
		@InjectRepository(UserWork)
		private readonly userWorkRepository: Repository<UserWork>,
		@InjectRepository(Work)
		private readonly workRepository: Repository<Work>,
		private readonly worksService: WorksService
	) {}

	async create(userId: number, createUserWorkDto: CreateUserWorkDto): Promise<UserWork> {
		const { workId, ...userWorkData } = createUserWorkDto;

		// Verificar se a obra existe
		const work = await this.workRepository.findOne({ where: { id: workId } });
		if (!work) {
			throw new NotFoundException("Obra não encontrada");
		}

		// Verificar se o usuário já tem essa obra na biblioteca
		const existingUserWork = await this.userWorkRepository.findOne({
			where: { userId, workId },
		});

		if (existingUserWork) {
			throw new ConflictException("Esta obra já está na sua biblioteca");
		}

		const userWork = this.userWorkRepository.create({
			userId,
			workId,
			...userWorkData,
		});

		const savedUserWork = await this.userWorkRepository.save(userWork);

		// Atualizar rating da obra se uma nota foi dada
		if (createUserWorkDto.rating) {
			await this.worksService.updateRating(workId);
		}

		return this.findOne(savedUserWork.id, userId);
	}

	async findAllByUser(
		userId: number,
		paginationDto: PaginationDto,
		filters?: {
			status?: ReadingStatus;
			isFavorite?: boolean;
			search?: string;
			tag?: string;
		}
	): Promise<PaginationResponseDto<UserWork>> {
		const { page = 1, limit = 10 } = paginationDto;
		const skip = (page - 1) * limit;

		const queryBuilder = this.userWorkRepository
			.createQueryBuilder("userWork")
			.leftJoinAndSelect("userWork.work", "work")
			.where("userWork.userId = :userId", { userId });

		// Aplicar filtros
		if (filters?.status) {
			queryBuilder.andWhere("userWork.status = :status", { status: filters.status });
		}

		if (filters?.isFavorite !== undefined) {
			queryBuilder.andWhere("userWork.isFavorite = :isFavorite", {
				isFavorite: filters.isFavorite,
			});
		}

		if (filters?.search) {
			queryBuilder.andWhere("(work.title ILIKE :search OR work.alternativeTitle ILIKE :search)", { search: `%${filters.search}%` });
		}

		if (filters?.tag) {
			queryBuilder.andWhere(":tag = ANY(userWork.tags)", { tag: filters.tag });
		}

		// Ordenação
		queryBuilder.orderBy("userWork.updatedAt", "DESC");

		// Paginação
		queryBuilder.skip(skip).take(limit);

		const [data, total] = await queryBuilder.getManyAndCount();

		const totalPages = Math.ceil(total / limit);

		return {
			data,
			meta: {
				page,
				limit,
				total,
				totalPages,
				hasNext: page < totalPages,
				hasPrev: page > 1,
			},
		};
	}

	async findOne(id: number, userId: number): Promise<UserWork> {
		const userWork = await this.userWorkRepository.findOne({
			where: { id, userId },
			relations: ["work", "user"],
		});

		if (!userWork) {
			throw new NotFoundException("Obra não encontrada na sua biblioteca");
		}

		return userWork;
	}

	async update(id: number, userId: number, updateUserWorkDto: UpdateUserWorkDto): Promise<UserWork> {
		const userWork = await this.findOne(id, userId);

		const oldRating = userWork.rating;
		Object.assign(userWork, updateUserWorkDto);

		const updatedUserWork = await this.userWorkRepository.save(userWork);

		// Atualizar rating da obra se a nota foi alterada
		if (updateUserWorkDto.rating !== undefined && updateUserWorkDto.rating !== oldRating) {
			await this.worksService.updateRating(userWork.workId);
		}

		return this.findOne(updatedUserWork.id, userId);
	}

	async remove(id: number, userId: number): Promise<void> {
		const userWork = await this.findOne(id, userId);
		const hadRating = userWork.rating !== null;

		await this.userWorkRepository.remove(userWork);

		// Atualizar rating da obra se tinha uma nota
		if (hadRating) {
			await this.worksService.updateRating(userWork.workId);
		}
	}

	async updateProgress(id: number, userId: number, chapter: number): Promise<UserWork> {
		const userWork = await this.findOne(id, userId);

		userWork.currentChapter = chapter;

		// Se chegou ao final, marcar como completo
		if (userWork.work.totalChapters && chapter >= userWork.work.totalChapters) {
			userWork.status = ReadingStatus.COMPLETED;
			if (!userWork.finishDate) {
				userWork.finishDate = new Date();
			}
		}

		return this.userWorkRepository.save(userWork);
	}

	async getStatistics(userId: number): Promise<{
		total: number;
		reading: number;
		completed: number;
		onHold: number;
		dropped: number;
		planToRead: number;
		favorites: number;
		averageRating: number;
	}> {
		const stats = await this.userWorkRepository
			.createQueryBuilder("userWork")
			.select("userWork.status", "status")
			.addSelect("COUNT(*)", "count")
			.addSelect("SUM(CASE WHEN userWork.isFavorite = true THEN 1 ELSE 0 END)", "favorites")
			.addSelect("AVG(userWork.rating)", "averageRating")
			.where("userWork.userId = :userId", { userId })
			.groupBy("userWork.status")
			.getRawMany();

		const result = {
			total: 0,
			reading: 0,
			completed: 0,
			onHold: 0,
			dropped: 0,
			planToRead: 0,
			favorites: 0,
			averageRating: 0,
		};

		let totalRatings = 0;
		let sumRatings = 0;

		for (const stat of stats) {
			const count = parseInt(stat.count);
			result.total += count;

			switch (stat.status) {
				case ReadingStatus.READING:
					result.reading = count;
					break;
				case ReadingStatus.COMPLETED:
					result.completed = count;
					break;
				case ReadingStatus.ON_HOLD:
					result.onHold = count;
					break;
				case ReadingStatus.DROPPED:
					result.dropped = count;
					break;
				case ReadingStatus.PLAN_TO_READ:
					result.planToRead = count;
					break;
			}

			if (stat.favorites) {
				result.favorites += parseInt(stat.favorites);
			}

			if (stat.averageRating) {
				const avgRating = parseFloat(stat.averageRating);
				sumRatings += avgRating * count;
				totalRatings += count;
			}
		}

		result.averageRating = totalRatings > 0 ? sumRatings / totalRatings : 0;

		return result;
	}
}

import { <PERSON>ti<PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, OneToMany } from "typeorm";
import { ApiProperty } from "@nestjs/swagger";
import { User } from "./user.entity";
import { RankingItem } from "./ranking-item.entity";

@Entity("rankings")
export class Ranking {
	@ApiProperty({
		description: "ID único do ranking",
		example: 1,
	})
	@PrimaryGeneratedColumn()
	id: number;

	@ApiProperty({
		description: "ID do usuário",
		example: 1,
	})
	@Column()
	userId: number;

	@ApiProperty({
		description: "Nome do ranking",
		example: "Meus Mangás Favoritos de 2024",
	})
	@Column()
	name: string;

	@ApiProperty({
		description: "Descrição do ranking",
		example: "Lista dos mangás que mais me impressionaram este ano",
	})
	@Column({ type: "text", nullable: true })
	description?: string;

	@ApiProperty({
		description: "Se o ranking é público",
		example: true,
	})
	@Column({ default: false })
	isPublic: boolean;

	@ApiProperty({
		description: "Cor do ranking (hex)",
		example: "#FF5733",
	})
	@Column({ nullable: true })
	color?: string;

	@ApiProperty({
		description: "Data de criação",
	})
	@CreateDateColumn()
	createdAt: Date;

	@ApiProperty({
		description: "Data de última atualização",
	})
	@UpdateDateColumn()
	updatedAt: Date;

	// Relacionamentos
	@ManyToOne(() => User, user => user.rankings, { onDelete: "CASCADE" })
	@JoinColumn({ name: "userId" })
	user: User;

	@OneToMany(() => RankingItem, rankingItem => rankingItem.ranking, {
		cascade: true,
	})
	items: RankingItem[];
}

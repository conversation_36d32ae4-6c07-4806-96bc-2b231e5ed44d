import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index } from "typeorm";
import { ApiProperty } from "@nestjs/swagger";
import { Work } from "./work.entity";

@Entity("rating_metrics")
@Index(["workId", "calculatedAt"])
export class RatingMetrics {
	@ApiProperty({
		description: "ID único da métrica",
		example: 1,
	})
	@PrimaryGeneratedColumn()
	id: number;

	@ApiProperty({
		description: "ID da obra",
		example: 1,
	})
	@Column()
	workId: number;

	@ApiProperty({
		description: "Média simples das avaliações",
		example: 8.5,
	})
	@Column({ type: "decimal", precision: 3, scale: 2 })
	simpleAverage: number;

	@ApiProperty({
		description: "Média ponderada (Bayesian Average)",
		example: 8.2,
	})
	@Column({ type: "decimal", precision: 3, scale: 2 })
	bayesianAverage: number;

	@ApiProperty({
		description: "Mediana das avaliações",
		example: 8.0,
	})
	@Column({ type: "decimal", precision: 3, scale: 2 })
	median: number;

	@ApiProperty({
		description: "Desvio padrão das avaliações",
		example: 1.2,
	})
	@Column({ type: "decimal", precision: 4, scale: 3 })
	standardDeviation: number;

	@ApiProperty({
		description: "Distribuição de notas por valor (1-10)",
		example: [0, 2, 5, 8, 12, 15, 20, 18, 12, 8],
		type: [Number],
	})
	@Column("simple-array", {
		transformer: {
			to: (value: number[]) => value?.join(","),
			from: (value: string) => (value ? value.split(",").map(Number) : []),
		},
	})
	distribution: number[];

	@ApiProperty({
		description: "Score de confiabilidade (0-100)",
		example: 85.5,
	})
	@Column({ type: "decimal", precision: 5, scale: 2 })
	confidenceScore: number;

	@ApiProperty({
		description: "Número de outliers detectados",
		example: 3,
	})
	@Column({ default: 0 })
	outliersCount: number;

	@ApiProperty({
		description: "Score de tendência (últimos 30 dias)",
		example: 0.15,
	})
	@Column({ type: "decimal", precision: 3, scale: 2, default: 0 })
	trendScore: number;

	@ApiProperty({
		description: "Velocidade de mudança do rating",
		example: 0.05,
	})
	@Column({ type: "decimal", precision: 4, scale: 3, default: 0 })
	ratingVelocity: number;

	@ApiProperty({
		description: "Score de qualidade geral",
		example: 92.3,
	})
	@Column({ type: "decimal", precision: 5, scale: 2 })
	qualityScore: number;

	@ApiProperty({
		description: "Número total de avaliações consideradas",
		example: 1250,
	})
	@Column()
	totalRatings: number;

	@ApiProperty({
		description: "Número de avaliações nos últimos 7 dias",
		example: 15,
	})
	@Column({ default: 0 })
	recentRatings7d: number;

	@ApiProperty({
		description: "Número de avaliações nos últimos 30 dias",
		example: 45,
	})
	@Column({ default: 0 })
	recentRatings30d: number;

	@ApiProperty({
		description: "Data do cálculo das métricas",
	})
	@Column()
	calculatedAt: Date;

	@ApiProperty({
		description: "Data de criação",
	})
	@CreateDateColumn()
	createdAt: Date;

	@ApiProperty({
		description: "Data de última atualização",
	})
	@UpdateDateColumn()
	updatedAt: Date;

	@ManyToOne(() => Work, work => work.ratingMetrics, { onDelete: "CASCADE" })
	@JoinColumn({ name: "workId" })
	work: Work;
}
